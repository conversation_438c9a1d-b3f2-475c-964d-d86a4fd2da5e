import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { ApiServer } from './apiServer';
import { requestLogger } from './shared/middleware/requestLogger';
import { errorHandler, notFoundHandler } from './shared/middleware/errorHandler';
import { logger } from './shared/utils/logger';
import {
  ApiKeyConfig,
  DatabaseConfig,
  EncryptionConfig,
  ApiResponse
} from './shared/types/ApiTypes';

// Load environment variables
dotenv.config();

class BackendServer {
  private readonly app: express.Application;
  private readonly port: number;
  private apiServer?: ApiServer;

  constructor() {
    this.app = express();
    this.port = parseInt(process.env.PORT || '3000', 10);
    
    this.setupMiddleware();
    this.setupServices();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet());
    
    // CORS middleware
    this.app.use(cors({
      origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
      credentials: true
    }));

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging middleware
    this.app.use(requestLogger);
  }

  private setupServices(): void {
    // Configuration objects
    const apiKeyConfig: ApiKeyConfig = {
      ttlHours: parseInt(process.env.API_KEY_TTL_HOURS || '24', 10),
      keyLength: parseInt(process.env.API_KEY_LENGTH || '32', 10)
    };

    const databaseConfig: DatabaseConfig = {
      path: process.env.DB_PATH || './data/database.sqlite'
    };

    const encryptionConfig: EncryptionConfig = {
      secret: process.env.ENCRYPTION_SECRET || 'default-secret-change-in-production',
      algorithm: process.env.ENCRYPTION_ALGORITHM || 'aes-256-gcm'
    };

    // Initialize services
    this.apiServer = new ApiServer(apiKeyConfig, databaseConfig, encryptionConfig);
  }

  private setupRoutes(): void {
    // Health check endpoint
    this.app.get('/health', (_req, res) => {
      const response: ApiResponse = {
        success: true,
        data: {
          service: 'BackendServer',
          status: 'healthy',
          timestamp: new Date().toISOString(),
          version: '1.0.0'
        },
        message: 'Backend server is running',
        timestamp: new Date().toISOString()
      };
      res.json(response);
    });

    // Mount service routers
    if (this.apiServer) {
      this.app.use('/api', this.apiServer.getRouter());
    }

    // Only API Server is used in this application
  }

  private setupErrorHandling(): void {
    // 404 handler
    this.app.use(notFoundHandler);

    // Global error handler
    this.app.use(errorHandler);
  }

  public async start(): Promise<void> {
    try {
      this.app.listen(this.port, () => {
        logger.info(`Backend server started successfully`, {
          port: this.port,
          environment: process.env.NODE_ENV || 'development',
          timestamp: new Date().toISOString()
        });
        console.log(`Backend server is running on port ${this.port}`);
        console.log(`Health check: http://localhost:${this.port}/health`);
        console.log(`API Server: http://localhost:${this.port}/api`);
        console.log(`Auth Server: http://localhost:${this.port}/auth`);
        console.log(`Other Server: http://localhost:${this.port}/other`);
      });
    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  public async shutdown(): Promise<void> {
    logger.info('Shutting down server...');
    
    if (this.apiServer) {
      await this.apiServer.cleanup();
    }
    
    logger.info('Server shutdown complete');
    process.exit(0);
  }
}

// Handle graceful shutdown
const server = new BackendServer();

process.on('SIGTERM', () => server.shutdown());
process.on('SIGINT', () => server.shutdown());

// Start the server
server.start().catch((error) => {
  logger.error('Failed to start server:', error);
  process.exit(1);
});

export default BackendServer;
