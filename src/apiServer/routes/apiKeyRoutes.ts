import { Router } from 'express';
import { ApiKeyController } from '../controllers/ApiKeyController';

export function createApiKeyRoutes(controller: ApiKeyController): Router {
  const router = Router();

  // Generate API key
  router.post('/generate', controller.generateApiKey);

  // Validate API key
  router.post('/validate', controller.validateApiKey);

  // Revoke API key
  router.delete('/revoke', controller.revokeApiKey);

  return router;
}
