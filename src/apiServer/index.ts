import { Router } from 'express';
import { Api<PERSON>eyGenerator } from './services/ApiKeyGenerator';
import { ApiKeyValidator } from './services/ApiKeyValidator';
import { ApiKeyRepository } from './repositories/ApiKeyRepository';
import { ApiKeyService } from './services/ApiKeyService';
import { ApiKeyController } from './controllers/ApiKeyController';
import { createApiKeyRoutes } from './routes/apiKeyRoutes';
import { EncryptionService } from '../shared/utils/EncryptionService';
import { 
  ApiKeyConfig, 
  DatabaseConfig, 
  EncryptionConfig 
} from '../shared/types/ApiTypes';

export class ApiServer {
  private readonly router: Router;
  private readonly repository: ApiKeyRepository;

  constructor(
    apiKeyConfig: ApiKeyConfig,
    databaseConfig: DatabaseConfig,
    encryptionConfig: EncryptionConfig
  ) {
    // initialize dependencies following dependency injection pattern
    this.repository = new ApiKeyRepository(databaseConfig);
    const encryptionService = new EncryptionService(encryptionConfig);
    const generator = new ApiKeyGenerator(apiKeyConfig);
    const validator = new ApiKeyValidator(this.repository, encryptionService);
    const service = new ApiKeyService(generator, validator, this.repository);
    const controller = new ApiKeyController(service);

    // create router with routes
    this.router = Router();
    this.router.use('/keys', createApiKeyRoutes(controller));
  }

  getRouter(): Router {
    return this.router;
  }

  async cleanup(): Promise<void> {
    // close database connection
    this.repository.close();
  }
}
