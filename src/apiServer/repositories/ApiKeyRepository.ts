// TODO: Update this once the db is decided

import * as sqlite3 from 'sqlite3';
import { IApiKeyRepository } from '../../shared/interfaces/IRepository';
import { IApiKey } from '../../shared/interfaces/IApiKey';
import { DatabaseConfig } from '../../shared/types/ApiTypes';

export class ApiKeyRepository implements IApiKeyRepository {
  private db: sqlite3.Database;
  private initPromise: Promise<void>;

  constructor(config: DatabaseConfig) {
    this.db = new sqlite3.Database(config.path);
    this.initPromise = this.initializeDatabase();
  }

  private initializeDatabase(): Promise<void> {
    return new Promise((resolve, reject) => {
      // combined query to create table and indexes in one go
      const initQuery = `
        CREATE TABLE IF NOT EXISTS api_keys (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id TEXT NOT NULL,
          api_key TEXT NOT NULL UNIQUE,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          expires_at DATETIME NOT NULL,
          is_active BOOLEAN DEFAULT 1,
          metadata TEXT
        );

        CREATE INDEX IF NOT EXISTS idx_user_id ON api_keys(user_id);
        CREATE INDEX IF NOT EXISTS idx_api_key ON api_keys(api_key);
        CREATE INDEX IF NOT EXISTS idx_expires_at ON api_keys(expires_at);
      `;

      // Execute all initialization in one transaction
      this.db.exec(initQuery, (err) => {
        if (err) {
          reject(new Error(`Failed to initialize database: ${err.message}`));
          return;
        }
        resolve();
      });
    });
  }

  private async ensureInitialized(): Promise<void> {
    await this.initPromise;
  }

  async create(apiKey: Omit<IApiKey, 'id'>): Promise<IApiKey> {
    await this.ensureInitialized();
    return new Promise((resolve, reject) => {
      const query = `
        INSERT INTO api_keys (user_id, api_key, created_at, expires_at, is_active, metadata)
        VALUES (?, ?, ?, ?, ?, ?)
      `;

      const metadata = apiKey.metadata ? JSON.stringify(apiKey.metadata) : null;

      this.db.run(
        query,
        [
          apiKey.userId,
          apiKey.apiKey,
          apiKey.createdAt.toISOString(),
          apiKey.expiresAt.toISOString(),
          apiKey.isActive ? 1 : 0,
          metadata
        ],
        function (err) {
          if (err) {
            reject(new Error(`Failed to create API key: ${err.message}`));
            return;
          }

          resolve({
            id: this.lastID,
            ...apiKey
          });
        }
      );
    });
  }

  async findByUserId(userId: string): Promise<IApiKey | null> {
    await this.ensureInitialized();
    return new Promise((resolve, reject) => {
      const query = `
        SELECT * FROM api_keys 
        WHERE user_id = ? AND is_active = 1 
        ORDER BY created_at DESC 
        LIMIT 1
      `;

      this.db.get(query, [userId], (err, row: any) => {
        if (err) {
          reject(new Error(`Failed to find API key by user ID: ${err.message}`));
          return;
        }

        if (!row) {
          resolve(null);
          return;
        }

        resolve(this.mapRowToApiKey(row));
      });
    });
  }

  async findByApiKey(apiKey: string): Promise<IApiKey | null> {
    await this.ensureInitialized();
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM api_keys WHERE api_key = ?`;

      this.db.get(query, [apiKey], (err, row: any) => {
        if (err) {
          reject(new Error(`Failed to find API key: ${err.message}`));
          return;
        }

        if (!row) {
          resolve(null);
          return;
        }

        resolve(this.mapRowToApiKey(row));
      });
    });
  }

  async update(id: number, updates: Partial<IApiKey>): Promise<IApiKey | null> {
    await this.ensureInitialized();
    return new Promise((resolve, reject) => {
      const fields: string[] = [];
      const values: any[] = [];

      if (updates.isActive !== undefined) {
        fields.push('is_active = ?');
        values.push(updates.isActive ? 1 : 0);
      }

      if (updates.metadata !== undefined) {
        fields.push('metadata = ?');
        values.push(JSON.stringify(updates.metadata));
      }

      if (fields.length === 0) {
        reject(new Error('No fields to update'));
        return;
      }

      values.push(id);
      const query = `UPDATE api_keys SET ${fields.join(', ')} WHERE id = ?`;

      this.db.run(query, values, (err) => {
        if (err) {
          reject(new Error(`Failed to update API key: ${err.message}`));
          return;
        }

        // fetch the updated record
        this.db.get('SELECT * FROM api_keys WHERE id = ?', [id], (err, row: any) => {
          if (err) {
            reject(new Error(`Failed to fetch updated API key: ${err.message}`));
            return;
          }

          resolve(row ? this.mapRowToApiKey(row) : null);
        });
      });
    });
  }

  async delete(id: number): Promise<boolean> {
    await this.ensureInitialized();
    return new Promise((resolve, reject) => {
      const query = `DELETE FROM api_keys WHERE id = ?`;

      this.db.run(query, [id], function (err) {
        if (err) {
          reject(new Error(`Failed to delete API key: ${err.message}`));
          return;
        }

        resolve(this.changes > 0);
      });
    });
  }

  async deleteExpired(): Promise<number> {
    await this.ensureInitialized();
    return new Promise((resolve, reject) => {
      const query = `DELETE FROM api_keys WHERE expires_at < datetime('now')`;

      this.db.run(query, function (err) {
        if (err) {
          reject(new Error(`Failed to delete expired API keys: ${err.message}`));
          return;
        }

        resolve(this.changes);
      });
    });
  }

  private mapRowToApiKey(row: any): IApiKey {
    return {
      id: row.id,
      userId: row.user_id,
      apiKey: row.api_key,
      createdAt: new Date(row.created_at),
      expiresAt: new Date(row.expires_at),
      isActive: Boolean(row.is_active),
      metadata: row.metadata ? JSON.parse(row.metadata) : undefined
    };
  }

  close(): void {
    this.db.close();
  }
}
