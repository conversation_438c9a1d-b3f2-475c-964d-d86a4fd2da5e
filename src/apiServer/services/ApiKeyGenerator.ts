import * as crypto from 'crypto';
import { IApiKeyGenerator } from '../../shared/interfaces/IServices';
import { ApiKeyConfig } from '../../shared/types/ApiTypes';

export class ApiKeyGenerator implements IApiKeyGenerator {
  private readonly config: ApiKeyConfig;

  constructor(config: ApiKeyConfig) {
    this.config = config;
  }

  generateApiKey(): string {
    try {
      // generate a cryptographically secure random API key
      const randomBytes = crypto.randomBytes(this.config.keyLength);
      const apiKey = randomBytes.toString('base64')
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
      
      // add a prefix for identification
      return `ak_${apiKey}`;
    } catch (error) {
      throw new Error(`Failed to generate API key: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  calculateExpirationDate(): Date {
    const now = new Date();
    const expirationDate = new Date(now.getTime() + (this.config.ttlHours * 60 * 60 * 1000));
    return expirationDate;
  }

  /**
   * Validates the format of an API key
   */
  isValidApiKeyFormat(apiKey: string): boolean {
    // Check if it starts with 'ak_' and has the expected length
    // base64 length + prefix
    const expectedLength = Math.ceil((this.config.keyLength * 4) / 3) + 3;
    return apiKey.startsWith('ak_') && apiKey.length >= expectedLength - 5 && apiKey.length <= expectedLength + 5;
  }
}
