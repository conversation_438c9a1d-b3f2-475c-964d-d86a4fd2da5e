import { IApiKeyValidator, IEncryptionService } from '../../shared/interfaces/IServices';
import { IApiKeyRepository } from '../../shared/interfaces/IRepository';
import { 
  IApiKeyValidationRequest, 
  IApiKeyValidationResult 
} from '../../shared/interfaces/IApiKey';

export class ApiKeyValidator implements IApiKeyValidator {
  private readonly repository: IApiKeyRepository;
  private readonly encryptionService: IEncryptionService;

  constructor(repository: IApiKeyRepository, encryptionService: IEncryptionService) {
    this.repository = repository;
    this.encryptionService = encryptionService;
  }

  async validateApiKey(request: IApiKeyValidationRequest): Promise<IApiKeyValidationResult> {
    try {
      // decrypt the API key from the request
      let decryptedApiKey: string;
      try {
        decryptedApiKey = this.encryptionService.decrypt(request.encryptedApiKey);
      } catch (error) {
        return {
          isValid: false,
          isExpired: false,
          error: 'Invalid encrypted API key format'
        };
      }

      // find the API key record for the user
      // TODO: userId is agenId, replace
      const apiKeyRecord = await this.repository.findByUserId(request.userId);
      
      if (!apiKeyRecord) {
        return {
          isValid: false,
          isExpired: false,
          error: 'No API key found for user'
        };
      }

      // check if the API key is active
      if (!apiKeyRecord.isActive) {
        return {
          isValid: false,
          isExpired: false,
          error: 'API key is inactive'
        };
      }

      // check if the API key matches
      if (apiKeyRecord.apiKey !== decryptedApiKey) {
        return {
          isValid: false,
          isExpired: false,
          error: 'API key mismatch'
        };
      }

      // check if the API key has expired
      const isExpired = this.isExpired(apiKeyRecord.expiresAt);
      if (isExpired) {
        // try to mark the key as inactive, but don't fail if update fails
        try {
          await this.repository.update(apiKeyRecord.id!, { isActive: false });
        } catch (error) {
          // TODO:
          // log error but continue with expired response
        }

        return {
          isValid: false,
          isExpired: true,
          apiKey: apiKeyRecord,
          error: 'API key has expired'
        };
      }

      // API key is valid
      return {
        isValid: true,
        isExpired: false,
        apiKey: apiKeyRecord
      };

    } catch (error) {
      return {
        isValid: false,
        isExpired: false,
        error: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  isExpired(expiresAt: Date): boolean {
    const now = new Date();
    return now > expiresAt;
  }
}
