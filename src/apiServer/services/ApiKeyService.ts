import { IApiKeyService, IApiKeyGenerator, IApiKeyValidator } from '../../shared/interfaces/IServices';
import { IApiKeyRepository } from '../../shared/interfaces/IRepository';
import { 
  IApiKeyCreateRequest, 
  IApiKeyValidationRequest, 
  IApiKeyGenerationResult, 
  IApiKeyValidationResult,
  IApiKey
} from '../../shared/interfaces/IApiKey';

export class ApiKeyService implements IApiKeyService {
  private readonly generator: IApiKeyGenerator;
  private readonly validator: IApiKeyValidator;
  private readonly repository: IApiKeyRepository;

  constructor(
    generator: IApiKeyGenerator,
    validator: IApiKeyValidator,
    repository: IApiKeyRepository
  ) {
    this.generator = generator;
    this.validator = validator;
    this.repository = repository;
  }

  async generateApiKey(request: IApiKeyCreateRequest): Promise<IApiKeyGenerationResult> {
    try {
      // check if user already has an active API key
      const existingKey = await this.repository.findByUserId(request.userId);
      if (existingKey && existingKey.isActive && !this.validator.isExpired(existingKey.expiresAt)) {
        return {
          success: false,
          error: 'User already has an active API key. Please revoke the existing key first.'
        };
      }

      // if there's an expired key, deactivate it
      if (existingKey && existingKey.isActive && this.validator.isExpired(existingKey.expiresAt)) {
        await this.repository.update(existingKey.id!, { isActive: false });
      }

      // generate new API key
      const apiKey = this.generator.generateApiKey();
      const expiresAt = this.generator.calculateExpirationDate();
      const createdAt = new Date();

      // create API key record
      const apiKeyRecord: Omit<IApiKey, 'id'> = {
        userId: request.userId,
        apiKey,
        createdAt,
        expiresAt,
        isActive: true,
        metadata: request.metadata
      };

      // save to database
      const savedKey = await this.repository.create(apiKeyRecord);

      return {
        success: true,
        apiKey: savedKey.apiKey,
        expiresAt: savedKey.expiresAt
      };

    } catch (error) {
      return {
        success: false,
        error: `Failed to generate API key: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  async validateApiKey(request: IApiKeyValidationRequest): Promise<IApiKeyValidationResult> {
    try {
      return await this.validator.validateApiKey(request);
    } catch (error) {
      return {
        isValid: false,
        isExpired: false,
        error: `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Revoke an API key for a user
   */
  async revokeApiKey(userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const existingKey = await this.repository.findByUserId(userId);
      if (!existingKey || !existingKey.id) {
        return {
          success: false,
          error: 'No active API key found for user'
        };
      }

      await this.repository.update(existingKey.id, { isActive: false });

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: `Failed to revoke API key: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Clean up expired API keys
   */
  async cleanupExpiredKeys(): Promise<{ success: boolean; deletedCount?: number; error?: string }> {
    try {
      const deletedCount = await this.repository.deleteExpired();
      return {
        success: true,
        deletedCount
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to cleanup expired keys: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
}
