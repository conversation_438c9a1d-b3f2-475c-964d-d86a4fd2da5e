import { Request, Response } from 'express';
import { IApiKeyService } from '../../shared/interfaces/IServices';
import { 
  IApiKeyCreateRequest, 
  IApiKeyValidationRequest 
} from '../../shared/interfaces/IApiKey';
import { ApiResponse } from '../../shared/types/ApiTypes';

export class ApiKeyController {
  private readonly apiKeyService: IApiKeyService;

  constructor(apiKeyService: IApiKeyService) {
    this.apiKeyService = apiKeyService;
  }

  /**
   * Generate a new API key
   * POST /api/keys/generate
   */
  generateApiKey = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId, metadata } = req.body;

      // validate required fields
      if (!userId) {
        const response: ApiResponse = {
          success: false,
          error: 'userId is required',
          timestamp: new Date().toISOString()
        };
        res.status(400).json(response);
        return;
      }

      const request: IApiKeyCreateRequest = {
        userId,
        metadata
      };

      // generate api key using the service
      const result = await this.apiKeyService.generateApiKey(request);

      if (result.success) {
        const response: ApiResponse = {
          success: true,
          data: {
            apiKey: result.apiKey,
            expiresAt: result.expiresAt
          },
          message: 'API key generated successfully',
          timestamp: new Date().toISOString()
        };
        res.status(201).json(response);
      } else {
        const response: ApiResponse = {
          success: false,
          error: result.error,
          timestamp: new Date().toISOString()
        };
        res.status(400).json(response);
      }

    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: `Internal server error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date().toISOString()
      };
      res.status(500).json(response);
    }
  };

  /**
   * Validate an API key
   * POST /api/keys/validate
   */
  validateApiKey = async (req: Request, res: Response): Promise<void> => {
    try {
      const { encryptedApiKey, userId } = req.body;

      // validate required fields
      if (!encryptedApiKey || !userId) {
        const response: ApiResponse = {
          success: false,
          error: 'encryptedApiKey and userId are required',
          timestamp: new Date().toISOString()
        };
        res.status(400).json(response);
        return;
      }

      const request: IApiKeyValidationRequest = {
        encryptedApiKey,
        userId
      };

      // validate api key using the service
      const result = await this.apiKeyService.validateApiKey(request);

      if (result.isValid) {
        const response: ApiResponse = {
          success: true,
          data: {
            isValid: true,
            apiKey: {
              userId: result.apiKey!.userId,
              createdAt: result.apiKey!.createdAt,
              expiresAt: result.apiKey!.expiresAt,
              metadata: result.apiKey!.metadata
            }
          },
          message: 'API key is valid',
          timestamp: new Date().toISOString()
        };
        res.status(200).json(response);
      } else {
        const statusCode = result.isExpired ? 401 : 403;
        const response: ApiResponse = {
          success: false,
          data: {
            isValid: false,
            isExpired: result.isExpired
          },
          error: result.error,
          timestamp: new Date().toISOString()
        };
        res.status(statusCode).json(response);
      }

    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: `Internal server error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date().toISOString()
      };
      res.status(500).json(response);
    }
  };

  /**
   * Revoke an API key
   * DELETE /api/keys/revoke
   */
  revokeApiKey = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.body;

      if (!userId) {
        const response: ApiResponse = {
          success: false,
          error: 'userId is required',
          timestamp: new Date().toISOString()
        };
        res.status(400).json(response);
        return;
      }

      const result = await this.apiKeyService.revokeApiKey(userId);

      if (result.success) {
        const response: ApiResponse = {
          success: true,
          message: 'API key revoked successfully',
          timestamp: new Date().toISOString()
        };
        res.status(200).json(response);
      } else {
        const response: ApiResponse = {
          success: false,
          error: result.error,
          timestamp: new Date().toISOString()
        };
        res.status(404).json(response);
      }

    } catch (error) {
      const response: ApiResponse = {
        success: false,
        error: `Internal server error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date().toISOString()
      };
      res.status(500).json(response);
    }
  };
}
