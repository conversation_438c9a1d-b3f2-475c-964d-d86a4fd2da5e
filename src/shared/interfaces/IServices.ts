import { 
  IApiKeyCreateRequest, 
  IApiKeyValidationRequest, 
  IApiKeyGenerationResult, 
  IApiKeyValidationResult 
} from './IApiKey';

export interface IApiKeyGenerator {
  generateApiKey(): string;
  calculateExpirationDate(): Date;
}

export interface IApiKeyValidator {
  validateApiKey(request: IApiKeyValidationRequest): Promise<IApiKeyValidationResult>;
  isExpired(expiresAt: Date): boolean;
}

export interface IEncryptionService {
  encrypt(text: string): string;
  decrypt(encryptedText: string): string;
}

export interface IApiKeyService {
  generateApiKey(request: IApiKeyCreateRequest): Promise<IApiKeyGenerationResult>;
  validateApiKey(request: IApiKeyValidationRequest): Promise<IApiKeyValidationResult>;
  revokeApiKey(userId: string): Promise<{ success: boolean; error?: string }>;
}
