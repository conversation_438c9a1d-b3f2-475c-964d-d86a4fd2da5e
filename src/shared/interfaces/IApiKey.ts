export interface IApiKey {
  id?: number;
  userId: string;
  apiKey: string;
  createdAt: Date;
  expiresAt: Date;
  isActive: boolean;
  metadata?: Record<string, any> | undefined;
}

export interface IApiKeyCreateRequest {
  userId: string;
  metadata?: Record<string, any>;
}

export interface IApiKeyValidationRequest {
  encryptedApiKey: string;
  userId: string;
}

export interface IApiKeyValidationResult {
  isValid: boolean;
  isExpired: boolean;
  apiKey?: IApiKey;
  error?: string;
}

export interface IApiKeyGenerationResult {
  success: boolean;
  apiKey?: string;
  expiresAt?: Date;
  error?: string;
}
