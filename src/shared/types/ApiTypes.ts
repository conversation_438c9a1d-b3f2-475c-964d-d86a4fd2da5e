export type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string | undefined;
  message?: string;
  timestamp: string;
};

export type ApiKeyMetadata = {
  createdBy?: string;
  purpose?: string;
  permissions?: string[];
  [key: string]: any;
};

export type DatabaseConfig = {
  path: string;
};

export type EncryptionConfig = {
  secret: string;
  algorithm: string;
};

export type ApiKeyConfig = {
  ttlHours: number;
  keyLength: number;
};
