# Backend Server - Monolithic Node.js API

A monolithic Node.js backend server with organized services, built with TypeScript and following SOLID principles.

## Architecture

```
backendServer/
├── src/
│   ├── app.ts                 # Main server entry point
│   ├── apiServer/             # API key management service
│   │   ├── controllers/       # HTTP request handlers
│   │   ├── services/          # Business logic
│   │   ├── repositories/      # Data access layer
│   │   └── routes/            # Route definitions
│   └── shared/                # Shared utilities and interfaces
│       ├── interfaces/        # TypeScript interfaces
│       ├── types/             # Type definitions
│       ├── utils/             # Utility functions
│       └── middleware/        # Express middleware
├── data/                      # SQLite database files
└── logs/                      # Application logs
```

## Features

### API Server Service
- **API Key Generation**: Generate secure API keys with TTL
- **API Key Validation**: Validate encrypted API keys
- **API Key Revocation**: Revoke active API keys
- **Automatic Cleanup**: Remove expired API keys

### Security Features
- Encrypted API key transmission
- Secure key generation using crypto.randomBytes
- Input validation and sanitization
- Request logging and monitoring
- CORS protection
- Helmet security headers

## Installation

1. **Clone and setup**:
   ```bash
   cd backendServer
   npm install
   ```

2. **Environment Configuration**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Build the project**:
   ```bash
   npm run build
   ```

## Usage

### Development
```bash
npm run dev
```

### Production
```bash
npm run build
npm start
```

## API Endpoints

### Health Check
```
GET /health
```

### API Key Management

#### Generate API Key
```
POST /api/keys/generate
Content-Type: application/json

{
  "userId": "user123",
  "metadata": {
    "purpose": "client-integration",
    "permissions": ["read", "write"]
  }
}
```

#### Validate API Key
```
POST /api/keys/validate
Content-Type: application/json

{
  "encryptedApiKey": "encrypted_key_here",
  "userId": "user123"
}
```

#### Revoke API Key
```
DELETE /api/keys/revoke
Content-Type: application/json

{
  "userId": "user123"
}
```

## Environment Variables

| Variable               | Description             | Default                  |
| ---------------------- | ----------------------- | ------------------------ |
| `PORT`                 | Server port             | `3000`                   |
| `NODE_ENV`             | Environment             | `development`            |
| `API_KEY_TTL_HOURS`    | API key expiration time | `24`                     |
| `API_KEY_LENGTH`       | API key byte length     | `32`                     |
| `ENCRYPTION_SECRET`    | Encryption secret key   | Required                 |
| `ENCRYPTION_ALGORITHM` | Encryption algorithm    | `aes-256-gcm`            |
| `DB_PATH`              | SQLite database path    | `./data/database.sqlite` |
| `LOG_LEVEL`            | Logging level           | `info`                   |
| `LOG_FILE`             | Log file path           | `./logs/app.log`         |

## API Key Encryption

API keys are passed encrypted in the request body and decrypted on the server side:

1. **Client Side**: Encrypt the API key using the same algorithm and secret
2. **Server Side**: Decrypt the API key and validate against stored keys

## Database Schema

### api_keys table
```sql
CREATE TABLE api_keys (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id TEXT NOT NULL,
  api_key TEXT NOT NULL UNIQUE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  expires_at DATETIME NOT NULL,
  is_active BOOLEAN DEFAULT 1,
  metadata TEXT
);
```

## Development

### Scripts
- `npm run dev` - Start development server with hot reload
- `npm run build` - Build TypeScript to JavaScript
- `npm start` - Start production server
- `npm test` - Run tests
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues

### Architecture

This backend server focuses on API key management with a clean, modular architecture:

- **API Server**: Complete API key management system
- **Shared Components**: Reusable utilities, middleware, and types
- **Comprehensive Testing**: Unit and integration tests with excellent coverage

## Testing

```bash
# Install dependencies
npm install

# Start server
npm run dev

# Test health endpoint
curl http://localhost:3000/health

# Test API key generation
curl -X POST http://localhost:3000/api/keys/generate \
  -H "Content-Type: application/json" \
  -d '{"userId": "test-user"}'
```

## License

MIT
