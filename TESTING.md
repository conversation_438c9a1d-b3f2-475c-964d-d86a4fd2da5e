# Testing Documentation

This document provides comprehensive information about testing the API Server service.

## Test Structure

```
tests/
├── setup.ts                    # Global test setup and configuration
├── utils/
│   └── testHelpers.ts          # Test utilities and helper functions
├── unit/                       # Unit tests
│   ├── EncryptionService.test.ts
│   ├── ApiKeyGenerator.test.ts
│   ├── ApiKeyValidator.test.ts
│   ├── ApiKeyRepository.test.ts
│   ├── ApiKeyService.test.ts
│   └── ApiKeyController.test.ts
└── integration/                # Integration tests
    ├── apiEndpoints.test.ts    # End-to-end API testing
    └── database.test.ts        # Database integration testing
```

## Test Categories

### Unit Tests
- **EncryptionService**: Tests encryption/decryption functionality
- **ApiKeyGenerator**: Tests API key generation and validation
- **ApiKeyValidator**: Tests API key validation logic with mocked dependencies
- **ApiKeyRepository**: Tests database operations with real SQLite database
- **ApiKeyService**: Tests business logic orchestration with mocked dependencies
- **ApiKeyController**: Tests HTTP request/response handling with mocked services

### Integration Tests
- **API Endpoints**: End-to-end testing of all API endpoints
- **Database**: Tests database operations, performance, and data integrity

## Running Tests

### All Tests
```bash
npm test
```

### Unit Tests Only
```bash
npm run test:unit
```

### Integration Tests Only
```bash
npm run test:integration
```

### Watch Mode (for development)
```bash
npm run test:watch
```

### Coverage Report
```bash
npm run test:coverage
```

## Test Configuration

### Environment Variables
Tests use the following environment variables (set in `tests/setup.ts`):
- `NODE_ENV=test`
- `DB_PATH=./data/test/test-database.sqlite`
- `LOG_LEVEL=error`
- `ENCRYPTION_SECRET=test-secret-key-for-testing-only`
- `ENCRYPTION_ALGORITHM=aes-256-cbc`
- `API_KEY_TTL_HOURS=1`
- `API_KEY_LENGTH=16`

### Test Database
- Tests use isolated SQLite databases in `./data/test/`
- Each test file uses its own database to prevent interference
- Databases are automatically cleaned up after tests

## Test Utilities

### Helper Functions
- `createMockApiKey()`: Creates mock API key objects
- `createExpiredApiKey()`: Creates expired API key objects
- `createTestConfigs()`: Creates test configuration objects
- `generateRandomUserId()`: Generates unique user IDs for tests
- `cleanupTestDatabase()`: Cleans up test database files

### Test Data
Tests use realistic but safe test data:
- User IDs: `test-user-123`, `user-{random}`
- API Keys: `ak_test-api-key-12345`
- Metadata: `{ purpose: 'testing' }`

## Coverage Goals

Target coverage metrics:
- **Lines**: > 90%
- **Functions**: > 95%
- **Branches**: > 85%
- **Statements**: > 90%

## Test Scenarios Covered

### Unit Test Scenarios

#### EncryptionService
- ✅ Basic encryption/decryption
- ✅ Round-trip consistency
- ✅ Different input types (empty, special chars)
- ✅ Error handling for invalid inputs
- ✅ Cross-instance compatibility

#### ApiKeyGenerator
- ✅ Key generation with proper format
- ✅ Uniqueness of generated keys
- ✅ Expiration date calculation
- ✅ Format validation
- ✅ Different configuration options

#### ApiKeyValidator
- ✅ Valid key validation
- ✅ Invalid key rejection
- ✅ Expired key handling
- ✅ Encryption/decryption integration
- ✅ Database error handling

#### ApiKeyRepository
- ✅ CRUD operations
- ✅ Query by user ID and API key
- ✅ Expired key cleanup
- ✅ Data integrity constraints
- ✅ Special character handling
- ✅ Date storage/retrieval

#### ApiKeyService
- ✅ Key generation workflow
- ✅ Key validation workflow
- ✅ Key revocation
- ✅ Duplicate key prevention
- ✅ Expired key replacement
- ✅ Error propagation

#### ApiKeyController
- ✅ HTTP request/response handling
- ✅ Input validation
- ✅ Status code mapping
- ✅ Error response formatting
- ✅ Response consistency

### Integration Test Scenarios

#### API Endpoints
- ✅ Complete API key lifecycle
- ✅ Multi-user scenarios
- ✅ Error handling
- ✅ Request validation
- ✅ Response format consistency

#### Database
- ✅ Concurrent operations
- ✅ Data integrity
- ✅ Performance with large datasets
- ✅ Transaction handling
- ✅ Multiple instance access

## Best Practices

### Writing Tests
1. **Arrange-Act-Assert**: Structure tests clearly
2. **Descriptive Names**: Use clear, descriptive test names
3. **Single Responsibility**: Each test should test one thing
4. **Independent Tests**: Tests should not depend on each other
5. **Cleanup**: Always clean up resources after tests

### Mocking Strategy
- **Unit Tests**: Mock external dependencies
- **Integration Tests**: Use real implementations
- **Database Tests**: Use isolated test databases
- **HTTP Tests**: Use supertest for real HTTP calls

### Test Data
- Use realistic but safe test data
- Generate unique identifiers to avoid conflicts
- Clean up test data after each test
- Use factories for consistent test object creation

## Debugging Tests

### Running Individual Tests
```bash
# Run specific test file
npx jest tests/unit/ApiKeyService.test.ts

# Run specific test case
npx jest -t "should generate API key successfully"

# Run with verbose output
npx jest --verbose
```

### Debug Mode
```bash
# Run tests in debug mode
node --inspect-brk node_modules/.bin/jest --runInBand
```

### Common Issues
1. **Database locks**: Ensure proper cleanup in afterEach
2. **Async operations**: Use proper async/await patterns
3. **Mock cleanup**: Reset mocks between tests
4. **Environment isolation**: Use separate test databases

## Continuous Integration

Tests are designed to run in CI environments:
- No external dependencies required
- Self-contained test databases
- Deterministic test results
- Fast execution (< 30 seconds for full suite)

## Performance Benchmarks

Expected test execution times:
- **Unit Tests**: < 10 seconds
- **Integration Tests**: < 20 seconds
- **Full Suite**: < 30 seconds

## Adding New Tests

When adding new functionality:
1. Write unit tests for individual components
2. Add integration tests for end-to-end workflows
3. Update test documentation
4. Ensure coverage targets are met
5. Test error scenarios and edge cases

## Test Maintenance

Regular maintenance tasks:
- Update test data when schemas change
- Review and update mocks when interfaces change
- Monitor test execution times
- Update coverage targets as codebase grows
- Review and refactor test utilities
