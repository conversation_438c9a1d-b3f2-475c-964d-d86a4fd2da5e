{"name": "backend-server", "version": "1.0.0", "description": "AGIConomy Test Backend", "main": "dist/app.js", "scripts": {"build": "tsc", "start": "node dist/app.js", "dev": "ts-node-dev --respawn --transpile-only src/app.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["nodejs", "typescript", "api", "monolithic", "backend"], "author": "", "license": "MIT", "dependencies": {"bcrypt": "^5.1.1", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "sqlite3": "^5.1.6", "winston": "^3.11.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.5", "@types/sqlite3": "^3.1.11", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "supertest": "^6.3.4", "ts-jest": "^29.4.0", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}}