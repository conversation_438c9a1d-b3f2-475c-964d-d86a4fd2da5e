/*
  @method: tests generated using AI
  @validation: manually validated
*/

import { ApiKeyRepository } from '../../src/apiServer/repositories/ApiKeyRepository';
import { DatabaseConfig } from '../../src/shared/types/ApiTypes';
import { createMockApiKey } from '../utils/testHelpers';

describe('ApiKeyRepository', () => {
  let repository: ApiKeyRepository;
  let config: DatabaseConfig;
  const testDbPath = ':memory:'; // Use in-memory database for reliable testing

  beforeEach(async () => {
    // No cleanup needed for in-memory database
    config = { path: testDbPath };
    repository = new ApiKeyRepository(config);

    // Wait for database initialization to complete
    await new Promise(resolve => setTimeout(resolve, 50));
  });

  afterEach(async () => {
    if (repository) {
      try {
        repository.close();
      } catch (error) {
        // Ignore close errors in tests
      }
    }
    // No cleanup needed for in-memory database
  });

  describe('create', () => {
    it('should create a new API key record', async () => {
      const apiKeyData = createMockApiKey();
      delete apiKeyData.id; // Remove ID for creation
      
      const result = await repository.create(apiKeyData);
      
      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
      expect(result.userId).toBe(apiKeyData.userId);
      expect(result.apiKey).toBe(apiKeyData.apiKey);
      expect(result.isActive).toBe(apiKeyData.isActive);
      expect(result.metadata).toEqual(apiKeyData.metadata);
    });

    it('should handle API key with no metadata', async () => {
      const apiKeyData = createMockApiKey({ metadata: undefined });
      delete apiKeyData.id;
      
      const result = await repository.create(apiKeyData);
      
      expect(result).toBeDefined();
      expect(result.metadata).toBeUndefined();
    });

    it('should reject duplicate API keys', async () => {
      const apiKeyData = createMockApiKey();
      delete apiKeyData.id;
      
      await repository.create(apiKeyData);
      
      await expect(repository.create(apiKeyData)).rejects.toThrow('Failed to create API key');
    });

    it('should handle complex metadata', async () => {
      const complexMetadata = {
        purpose: 'testing',
        permissions: ['read', 'write'],
        nested: { key: 'value', array: [1, 2, 3] },
        timestamp: new Date().toISOString()
      };
      
      const apiKeyData = createMockApiKey({ metadata: complexMetadata });
      delete apiKeyData.id;
      
      const result = await repository.create(apiKeyData);
      
      expect(result.metadata).toEqual(complexMetadata);
    });
  });

  describe('findByUserId', () => {
    it('should find API key by user ID', async () => {
      const apiKeyData = createMockApiKey();
      delete apiKeyData.id;
      
      const created = await repository.create(apiKeyData);
      const found = await repository.findByUserId(apiKeyData.userId);
      
      expect(found).toBeDefined();
      expect(found!.id).toBe(created.id);
      expect(found!.userId).toBe(apiKeyData.userId);
    });

    it('should return null for non-existent user', async () => {
      const found = await repository.findByUserId('non-existent-user');
      
      expect(found).toBeNull();
    });

    it('should return only active API keys', async () => {
      const activeKey = createMockApiKey({ userId: 'test-user', isActive: true });
      const inactiveKey = createMockApiKey({ userId: 'test-user', isActive: false, apiKey: 'ak_different' });
      delete activeKey.id;
      delete inactiveKey.id;
      
      await repository.create(inactiveKey);
      await repository.create(activeKey);
      
      const found = await repository.findByUserId('test-user');
      
      expect(found).toBeDefined();
      expect(found!.isActive).toBe(true);
      expect(found!.apiKey).toBe(activeKey.apiKey);
    });

    it('should return most recent API key when multiple exist', async () => {
      const userId = 'test-user';
      const oldKey = createMockApiKey({ 
        userId, 
        apiKey: 'ak_old',
        createdAt: new Date(Date.now() - 60000) // 1 minute ago
      });
      const newKey = createMockApiKey({ 
        userId, 
        apiKey: 'ak_new',
        createdAt: new Date() // now
      });
      delete oldKey.id;
      delete newKey.id;
      
      await repository.create(oldKey);
      await repository.create(newKey);
      
      const found = await repository.findByUserId(userId);
      
      expect(found).toBeDefined();
      expect(found!.apiKey).toBe(newKey.apiKey);
    });
  });

  describe('findByApiKey', () => {
    it('should find API key by key value', async () => {
      const apiKeyData = createMockApiKey();
      delete apiKeyData.id;
      
      await repository.create(apiKeyData);
      const found = await repository.findByApiKey(apiKeyData.apiKey);
      
      expect(found).toBeDefined();
      expect(found!.apiKey).toBe(apiKeyData.apiKey);
      expect(found!.userId).toBe(apiKeyData.userId);
    });

    it('should return null for non-existent API key', async () => {
      const found = await repository.findByApiKey('ak_non-existent');
      
      expect(found).toBeNull();
    });

    it('should find inactive API keys', async () => {
      const inactiveKey = createMockApiKey({ isActive: false });
      delete inactiveKey.id;
      
      await repository.create(inactiveKey);
      const found = await repository.findByApiKey(inactiveKey.apiKey);
      
      expect(found).toBeDefined();
      expect(found!.isActive).toBe(false);
    });
  });

  describe('update', () => {
    it('should update API key record', async () => {
      const apiKeyData = createMockApiKey();
      delete apiKeyData.id;
      
      const created = await repository.create(apiKeyData);
      const updated = await repository.update(created.id!, { isActive: false });
      
      expect(updated).toBeDefined();
      expect(updated!.isActive).toBe(false);
      expect(updated!.id).toBe(created.id);
    });

    it('should update metadata', async () => {
      const apiKeyData = createMockApiKey();
      delete apiKeyData.id;
      
      const created = await repository.create(apiKeyData);
      const newMetadata = { updated: true, timestamp: new Date().toISOString() };
      const updated = await repository.update(created.id!, { metadata: newMetadata });
      
      expect(updated).toBeDefined();
      expect(updated!.metadata).toEqual(newMetadata);
    });

    it('should return null for non-existent ID', async () => {
      const updated = await repository.update(99999, { isActive: false });
      
      expect(updated).toBeNull();
    });

    it('should handle multiple field updates', async () => {
      const apiKeyData = createMockApiKey();
      delete apiKeyData.id;
      
      const created = await repository.create(apiKeyData);
      const updates = {
        isActive: false,
        metadata: { reason: 'revoked' }
      };
      const updated = await repository.update(created.id!, updates);
      
      expect(updated).toBeDefined();
      expect(updated!.isActive).toBe(false);
      expect(updated!.metadata).toEqual({ reason: 'revoked' });
    });
  });

  describe('delete', () => {
    it('should delete API key record', async () => {
      const apiKeyData = createMockApiKey();
      delete apiKeyData.id;
      
      const created = await repository.create(apiKeyData);
      const deleted = await repository.delete(created.id!);
      
      expect(deleted).toBe(true);
      
      const found = await repository.findByApiKey(apiKeyData.apiKey);
      expect(found).toBeNull();
    });

    it('should return false for non-existent ID', async () => {
      const deleted = await repository.delete(99999);
      
      expect(deleted).toBe(false);
    });
  });

  describe('deleteExpired', () => {
    it('should delete expired API keys', async () => {
      const activeKey = createMockApiKey();
      // Create a key that is definitely expired (use a very old date)
      const veryOldDate = new Date('2020-01-01T00:00:00.000Z');
      const expiredKey = createMockApiKey({
        apiKey: 'ak_expired',
        expiresAt: veryOldDate
      });
      delete activeKey.id;
      delete expiredKey.id;

      await repository.create(activeKey);
      await repository.create(expiredKey);

      const deletedCount = await repository.deleteExpired();

      expect(deletedCount).toBeGreaterThanOrEqual(1);

      const foundActive = await repository.findByApiKey(activeKey.apiKey);
      const foundExpired = await repository.findByApiKey(expiredKey.apiKey);

      expect(foundActive).toBeDefined();
      expect(foundExpired).toBeNull();
    });

    it('should return 0 when no expired keys exist', async () => {
      const activeKey = createMockApiKey();
      delete activeKey.id;
      
      await repository.create(activeKey);
      
      const deletedCount = await repository.deleteExpired();
      
      expect(deletedCount).toBe(0);
    });

    it('should handle empty database', async () => {
      const deletedCount = await repository.deleteExpired();
      
      expect(deletedCount).toBe(0);
    });
  });

  describe('error handling', () => {
    it('should handle database errors gracefully', async () => {
      // Close the current repository to simulate database errors
      repository.close();

      const apiKeyData = createMockApiKey();
      delete apiKeyData.id;

      // Try to use closed repository
      await expect(repository.create(apiKeyData)).rejects.toThrow();
    });
  });

  describe('date handling', () => {
    it('should correctly store and retrieve dates', async () => {
      const now = new Date();
      const future = new Date(now.getTime() + 60 * 60 * 1000);
      
      const apiKeyData = createMockApiKey({
        createdAt: now,
        expiresAt: future
      });
      delete apiKeyData.id;
      
      const created = await repository.create(apiKeyData);
      const found = await repository.findByApiKey(created.apiKey);
      
      expect(found).toBeDefined();
      expect(found!.createdAt.getTime()).toBeCloseTo(now.getTime(), -3);
      expect(found!.expiresAt.getTime()).toBeCloseTo(future.getTime(), -3);
    });
  });
});
