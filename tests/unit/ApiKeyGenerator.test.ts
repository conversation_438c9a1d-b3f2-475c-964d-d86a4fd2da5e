/*
  @method: tests generated using AI
  @validation: manually validated
*/

import { ApiKeyGenerator } from '../../src/apiServer/services/ApiKeyGenerator';
import { ApiKeyConfig } from '../../src/shared/types/ApiTypes';

describe('ApiKeyGenerator', () => {
  let generator: ApiKeyGenerator;
  let config: ApiKeyConfig;

  beforeEach(() => {
    config = {
      ttlHours: 24,
      keyLength: 32
    };
    generator = new ApiKeyGenerator(config);
  });

  describe('generateApiKey', () => {
    it('should generate a valid API key', () => {
      const apiKey = generator.generateApiKey();
      
      expect(apiKey).toBeDefined();
      expect(typeof apiKey).toBe('string');
      expect(apiKey).toMatch(/^ak_/); // Should start with 'ak_'
      expect(apiKey.length).toBeGreaterThan(10);
    });

    it('should generate unique API keys', () => {
      const keys = new Set();
      const numKeys = 100;
      
      for (let i = 0; i < numKeys; i++) {
        const key = generator.generateApiKey();
        keys.add(key);
      }
      
      expect(keys.size).toBe(numKeys); // All keys should be unique
    });

    it('should generate keys with consistent format', () => {
      const apiKey = generator.generateApiKey();
      
      expect(apiKey).toMatch(/^ak_[A-Za-z0-9_-]+$/);
      expect(apiKey).not.toContain('+');
      expect(apiKey).not.toContain('/');
      expect(apiKey).not.toContain('=');
    });

    it('should generate keys of appropriate length based on config', () => {
      const shortConfig: ApiKeyConfig = { ttlHours: 24, keyLength: 16 };
      const longConfig: ApiKeyConfig = { ttlHours: 24, keyLength: 64 };
      
      const shortGenerator = new ApiKeyGenerator(shortConfig);
      const longGenerator = new ApiKeyGenerator(longConfig);
      
      const shortKey = shortGenerator.generateApiKey();
      const longKey = longGenerator.generateApiKey();
      
      expect(longKey.length).toBeGreaterThan(shortKey.length);
    });

    it('should handle edge case of very small key length', () => {
      const smallConfig: ApiKeyConfig = { ttlHours: 24, keyLength: 1 };
      const smallGenerator = new ApiKeyGenerator(smallConfig);
      
      const apiKey = smallGenerator.generateApiKey();
      
      expect(apiKey).toBeDefined();
      expect(apiKey).toMatch(/^ak_/);
    });
  });

  describe('calculateExpirationDate', () => {
    it('should calculate correct expiration date', () => {
      const before = new Date();
      const expirationDate = generator.calculateExpirationDate();
      const after = new Date();
      
      const expectedMinTime = before.getTime() + (config.ttlHours * 60 * 60 * 1000);
      const expectedMaxTime = after.getTime() + (config.ttlHours * 60 * 60 * 1000);
      
      expect(expirationDate.getTime()).toBeGreaterThanOrEqual(expectedMinTime);
      expect(expirationDate.getTime()).toBeLessThanOrEqual(expectedMaxTime);
    });

    it('should calculate different expiration dates for different TTL configs', () => {
      const shortConfig: ApiKeyConfig = { ttlHours: 1, keyLength: 32 };
      const longConfig: ApiKeyConfig = { ttlHours: 48, keyLength: 32 };
      
      const shortGenerator = new ApiKeyGenerator(shortConfig);
      const longGenerator = new ApiKeyGenerator(longConfig);
      
      const now = new Date();
      const shortExpiration = shortGenerator.calculateExpirationDate();
      const longExpiration = longGenerator.calculateExpirationDate();
      
      expect(longExpiration.getTime()).toBeGreaterThan(shortExpiration.getTime());
      
      // Check approximate differences
      const shortDiff = shortExpiration.getTime() - now.getTime();
      const longDiff = longExpiration.getTime() - now.getTime();
      
      expect(shortDiff).toBeCloseTo(1 * 60 * 60 * 1000, -4); // 1 hour ±10s
      expect(longDiff).toBeCloseTo(48 * 60 * 60 * 1000, -4); // 48 hours ±10s
    });

    it('should always return future dates', () => {
      const now = new Date();
      const expirationDate = generator.calculateExpirationDate();
      
      expect(expirationDate.getTime()).toBeGreaterThan(now.getTime());
    });

    it('should handle zero TTL gracefully', () => {
      const zeroConfig: ApiKeyConfig = { ttlHours: 0, keyLength: 32 };
      const zeroGenerator = new ApiKeyGenerator(zeroConfig);
      
      const now = new Date();
      const expirationDate = zeroGenerator.calculateExpirationDate();
      
      // Should be approximately the same time (within a few milliseconds)
      expect(Math.abs(expirationDate.getTime() - now.getTime())).toBeLessThan(1000);
    });
  });

  describe('isValidApiKeyFormat', () => {
    it('should validate correct API key format', () => {
      const validKey = generator.generateApiKey();
      
      const isValid = generator.isValidApiKeyFormat(validKey);
      
      expect(isValid).toBe(true);
    });

    it('should reject keys without ak_ prefix', () => {
      const invalidKey = 'invalid-key-without-prefix';
      
      const isValid = generator.isValidApiKeyFormat(invalidKey);
      
      expect(isValid).toBe(false);
    });

    it('should reject keys that are too short', () => {
      const shortKey = 'ak_123';
      
      const isValid = generator.isValidApiKeyFormat(shortKey);
      
      expect(isValid).toBe(false);
    });

    it('should reject empty strings', () => {
      const emptyKey = '';
      
      const isValid = generator.isValidApiKeyFormat(emptyKey);
      
      expect(isValid).toBe(false);
    });

    it('should handle edge cases', () => {
      const testCases = [
        { key: 'ak_', expected: false },
        { key: 'ak_a', expected: false },
        { key: 'notanapi_key', expected: false },
        { key: 'ak_' + 'a'.repeat(100), expected: false }, // Very long key should be rejected
      ];

      testCases.forEach(({ key, expected }) => {
        const isValid = generator.isValidApiKeyFormat(key);
        expect(isValid).toBe(expected);
      });
    });
  });

  describe('error handling', () => {
    it('should handle crypto errors gracefully', () => {
      // Mock crypto.randomBytes to throw an error
      const originalRandomBytes = require('crypto').randomBytes;
      require('crypto').randomBytes = jest.fn().mockImplementation(() => {
        throw new Error('Crypto error');
      });
      
      expect(() => {
        generator.generateApiKey();
      }).toThrow('Failed to generate API key');
      
      // Restore original function
      require('crypto').randomBytes = originalRandomBytes;
    });
  });

  describe('configuration validation', () => {
    it('should work with different valid configurations', () => {
      const configs = [
        { ttlHours: 1, keyLength: 16 },
        { ttlHours: 24, keyLength: 32 },
        { ttlHours: 168, keyLength: 64 }, // 1 week
        { ttlHours: 8760, keyLength: 128 }, // 1 year
      ];
      
      configs.forEach(config => {
        const gen = new ApiKeyGenerator(config);
        const key = gen.generateApiKey();
        const expiration = gen.calculateExpirationDate();
        
        expect(key).toMatch(/^ak_/);
        expect(expiration).toBeInstanceOf(Date);
        expect(gen.isValidApiKeyFormat(key)).toBe(true);
      });
    });
  });
});
