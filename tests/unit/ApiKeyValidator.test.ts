/*
  @method: tests generated using AI
  @validation: manually validated
*/

import { ApiKeyValidator } from '../../src/apiServer/services/ApiKeyValidator';
import { IApiKeyRepository } from '../../src/shared/interfaces/IRepository';
import { IEncryptionService } from '../../src/shared/interfaces/IServices';
import { IApiKeyValidationRequest } from '../../src/shared/interfaces/IApiKey';
import { createMockApiKey, createExpiredApiKey } from '../utils/testHelpers';

describe('ApiKeyValidator', () => {
  let validator: ApiKeyValidator;
  let mockRepository: jest.Mocked<IApiKeyRepository>;
  let mockEncryptionService: jest.Mocked<IEncryptionService>;

  beforeEach(() => {
    mockRepository = {
      create: jest.fn(),
      findByUserId: jest.fn(),
      findByApiKey: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      deleteExpired: jest.fn(),
    };

    mockEncryptionService = {
      encrypt: jest.fn(),
      decrypt: jest.fn(),
    };

    validator = new ApiKeyValidator(mockRepository, mockEncryptionService);
  });

  describe('validateApiKey', () => {
    const validRequest: IApiKeyValidationRequest = {
      encryptedApiKey: 'encrypted-key',
      userId: 'test-user-123'
    };

    it('should validate a valid API key successfully', async () => {
      const mockApiKey = createMockApiKey();
      const decryptedKey = 'ak_test-api-key-12345';
      
      mockEncryptionService.decrypt.mockReturnValue(decryptedKey);
      mockRepository.findByUserId.mockResolvedValue(mockApiKey);

      const result = await validator.validateApiKey(validRequest);

      expect(result.isValid).toBe(true);
      expect(result.isExpired).toBe(false);
      expect(result.apiKey).toEqual(mockApiKey);
      expect(result.error).toBeUndefined();
      expect(mockEncryptionService.decrypt).toHaveBeenCalledWith('encrypted-key');
      expect(mockRepository.findByUserId).toHaveBeenCalledWith('test-user-123');
    });

    it('should reject invalid encrypted API key format', async () => {
      mockEncryptionService.decrypt.mockImplementation(() => {
        throw new Error('Invalid format');
      });

      const result = await validator.validateApiKey(validRequest);

      expect(result.isValid).toBe(false);
      expect(result.isExpired).toBe(false);
      expect(result.error).toBe('Invalid encrypted API key format');
      expect(mockRepository.findByUserId).not.toHaveBeenCalled();
    });

    it('should reject when no API key found for user', async () => {
      mockEncryptionService.decrypt.mockReturnValue('ak_test-key');
      mockRepository.findByUserId.mockResolvedValue(null);

      const result = await validator.validateApiKey(validRequest);

      expect(result.isValid).toBe(false);
      expect(result.isExpired).toBe(false);
      expect(result.error).toBe('No API key found for user');
    });

    it('should reject inactive API key', async () => {
      const inactiveApiKey = createMockApiKey({ isActive: false });
      
      mockEncryptionService.decrypt.mockReturnValue('ak_test-key');
      mockRepository.findByUserId.mockResolvedValue(inactiveApiKey);

      const result = await validator.validateApiKey(validRequest);

      expect(result.isValid).toBe(false);
      expect(result.isExpired).toBe(false);
      expect(result.error).toBe('API key is inactive');
    });

    it('should reject when API key does not match', async () => {
      const mockApiKey = createMockApiKey({ apiKey: 'ak_different-key' });
      
      mockEncryptionService.decrypt.mockReturnValue('ak_test-key');
      mockRepository.findByUserId.mockResolvedValue(mockApiKey);

      const result = await validator.validateApiKey(validRequest);

      expect(result.isValid).toBe(false);
      expect(result.isExpired).toBe(false);
      expect(result.error).toBe('API key mismatch');
    });

    it('should reject expired API key and mark it inactive', async () => {
      const expiredApiKey = createExpiredApiKey({ id: 1 });
      
      mockEncryptionService.decrypt.mockReturnValue(expiredApiKey.apiKey);
      mockRepository.findByUserId.mockResolvedValue(expiredApiKey);
      mockRepository.update.mockResolvedValue(null);

      const result = await validator.validateApiKey(validRequest);

      expect(result.isValid).toBe(false);
      expect(result.isExpired).toBe(true);
      expect(result.apiKey).toEqual(expiredApiKey);
      expect(result.error).toBe('API key has expired');
      expect(mockRepository.update).toHaveBeenCalledWith(1, { isActive: false });
    });

    it('should handle repository errors gracefully', async () => {
      mockEncryptionService.decrypt.mockReturnValue('ak_test-key');
      mockRepository.findByUserId.mockRejectedValue(new Error('Database error'));

      const result = await validator.validateApiKey(validRequest);

      expect(result.isValid).toBe(false);
      expect(result.isExpired).toBe(false);
      expect(result.error).toContain('Validation error: Database error');
    });

    it('should handle update errors when marking expired key inactive', async () => {
      const expiredApiKey = createExpiredApiKey({ id: 1 });

      mockEncryptionService.decrypt.mockReturnValue(expiredApiKey.apiKey);
      mockRepository.findByUserId.mockResolvedValue(expiredApiKey);
      mockRepository.update.mockRejectedValue(new Error('Update failed'));

      const result = await validator.validateApiKey(validRequest);

      expect(result.isValid).toBe(false);
      expect(result.isExpired).toBe(true);
      expect(result.apiKey).toEqual(expiredApiKey);
      expect(result.error).toBe('API key has expired');
    });
  });

  describe('isExpired', () => {
    it('should return true for past dates', () => {
      const pastDate = new Date(Date.now() - 60 * 60 * 1000); // 1 hour ago
      
      const result = validator.isExpired(pastDate);
      
      expect(result).toBe(true);
    });

    it('should return false for future dates', () => {
      const futureDate = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now
      
      const result = validator.isExpired(futureDate);
      
      expect(result).toBe(false);
    });

    it('should return true for current time (edge case)', async () => {
      const now = new Date();

      // Wait a tiny bit to ensure the date is in the past
      await new Promise(resolve => setTimeout(resolve, 10));
      const result = validator.isExpired(now);
      expect(result).toBe(true);
    });

    it('should handle edge cases correctly', () => {
      const testCases = [
        { date: new Date(0), expected: true }, // Unix epoch
        { date: new Date('2000-01-01'), expected: true }, // Past date
        { date: new Date('2099-12-31'), expected: false }, // Future date
      ];

      testCases.forEach(({ date, expected }) => {
        const result = validator.isExpired(date);
        expect(result).toBe(expected);
      });
    });
  });

  describe('error scenarios', () => {
    it('should handle encryption service throwing unexpected errors', async () => {
      mockEncryptionService.decrypt.mockImplementation(() => {
        throw new Error('Unexpected crypto error');
      });

      const result = await validator.validateApiKey({
        encryptedApiKey: 'encrypted-key',
        userId: 'test-user'
      });

      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Invalid encrypted API key format');
    });

    it('should handle missing API key ID when updating expired key', async () => {
      const expiredApiKey = createExpiredApiKey();
      delete expiredApiKey.id;

      mockEncryptionService.decrypt.mockReturnValue(expiredApiKey.apiKey);
      mockRepository.findByUserId.mockResolvedValue(expiredApiKey);

      const result = await validator.validateApiKey({
        encryptedApiKey: 'encrypted-key',
        userId: 'test-user'
      });

      expect(result.isValid).toBe(false);
      expect(result.isExpired).toBe(true);
      // The update should still be called even without ID, but will fail gracefully
      expect(mockRepository.update).toHaveBeenCalledWith(undefined, { isActive: false });
    });
  });

  describe('integration with different scenarios', () => {
    it('should handle multiple validation requests correctly', async () => {
      const mockApiKey1 = createMockApiKey({ userId: 'user1', apiKey: 'ak_key1' });
      const mockApiKey2 = createMockApiKey({ userId: 'user2', apiKey: 'ak_key2' });
      
      mockEncryptionService.decrypt
        .mockReturnValueOnce('ak_key1')
        .mockReturnValueOnce('ak_key2');
      
      mockRepository.findByUserId
        .mockResolvedValueOnce(mockApiKey1)
        .mockResolvedValueOnce(mockApiKey2);

      const result1 = await validator.validateApiKey({
        encryptedApiKey: 'encrypted-key1',
        userId: 'user1'
      });

      const result2 = await validator.validateApiKey({
        encryptedApiKey: 'encrypted-key2',
        userId: 'user2'
      });

      expect(result1.isValid).toBe(true);
      expect(result2.isValid).toBe(true);
      expect(mockEncryptionService.decrypt).toHaveBeenCalledTimes(2);
      expect(mockRepository.findByUserId).toHaveBeenCalledTimes(2);
    });
  });
});
