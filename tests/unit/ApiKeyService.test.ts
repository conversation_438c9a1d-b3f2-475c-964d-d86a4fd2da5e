/*
  @method: tests generated using AI
  @validation: manually validated
*/

import { ApiKeyService } from '../../src/apiServer/services/ApiKeyService';
import { IApiKeyGenerator, IApiKeyValidator } from '../../src/shared/interfaces/IServices';
import { IApiKeyRepository } from '../../src/shared/interfaces/IRepository';
import { IApiKeyCreateRequest, IApiKeyValidationRequest } from '../../src/shared/interfaces/IApiKey';
import { createMockApiKey, createExpiredApiKey } from '../utils/testHelpers';

describe('ApiKeyService', () => {
  let service: ApiKeyService;
  let mockGenerator: jest.Mocked<IApiKeyGenerator>;
  let mockValidator: jest.Mocked<IApiKeyValidator>;
  let mockRepository: jest.Mocked<IApiKeyRepository>;

  beforeEach(() => {
    mockGenerator = {
      generateApiKey: jest.fn(),
      calculateExpirationDate: jest.fn(),
    };

    mockValidator = {
      validateApiKey: jest.fn(),
      isExpired: jest.fn(),
    };

    mockRepository = {
      create: jest.fn(),
      findByUserId: jest.fn(),
      findByApiKey: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      deleteExpired: jest.fn(),
    };

    service = new ApiKeyService(mockGenerator, mockValidator, mockRepository);
  });

  describe('generateApiKey', () => {
    const validRequest: IApiKeyCreateRequest = {
      userId: 'test-user-123',
      metadata: { purpose: 'testing' }
    };

    it('should generate API key successfully for new user', async () => {
      const generatedKey = 'ak_generated-key-12345';
      const expirationDate = new Date(Date.now() + 60 * 60 * 1000);
      const createdApiKey = createMockApiKey({ 
        apiKey: generatedKey, 
        expiresAt: expirationDate 
      });

      mockRepository.findByUserId.mockResolvedValue(null);
      mockGenerator.generateApiKey.mockReturnValue(generatedKey);
      mockGenerator.calculateExpirationDate.mockReturnValue(expirationDate);
      mockRepository.create.mockResolvedValue(createdApiKey);

      const result = await service.generateApiKey(validRequest);

      expect(result.success).toBe(true);
      expect(result.apiKey).toBe(generatedKey);
      expect(result.expiresAt).toBe(expirationDate);
      expect(result.error).toBeUndefined();
      expect(mockRepository.create).toHaveBeenCalledWith({
        userId: validRequest.userId,
        apiKey: generatedKey,
        createdAt: expect.any(Date),
        expiresAt: expirationDate,
        isActive: true,
        metadata: validRequest.metadata
      });
    });

    it('should reject when user already has active API key', async () => {
      const existingKey = createMockApiKey({ isActive: true });
      
      mockRepository.findByUserId.mockResolvedValue(existingKey);
      mockValidator.isExpired.mockReturnValue(false);

      const result = await service.generateApiKey(validRequest);

      expect(result.success).toBe(false);
      expect(result.error).toBe('User already has an active API key. Please revoke the existing key first.');
      expect(mockGenerator.generateApiKey).not.toHaveBeenCalled();
      expect(mockRepository.create).not.toHaveBeenCalled();
    });

    it('should generate new key when existing key is expired', async () => {
      const expiredKey = createExpiredApiKey({ id: 1, isActive: true });
      const generatedKey = 'ak_new-key-12345';
      const expirationDate = new Date(Date.now() + 60 * 60 * 1000);
      const createdApiKey = createMockApiKey({ 
        apiKey: generatedKey, 
        expiresAt: expirationDate 
      });

      mockRepository.findByUserId.mockResolvedValue(expiredKey);
      mockValidator.isExpired.mockReturnValue(true);
      mockRepository.update.mockResolvedValue(null);
      mockGenerator.generateApiKey.mockReturnValue(generatedKey);
      mockGenerator.calculateExpirationDate.mockReturnValue(expirationDate);
      mockRepository.create.mockResolvedValue(createdApiKey);

      const result = await service.generateApiKey(validRequest);

      expect(result.success).toBe(true);
      expect(result.apiKey).toBe(generatedKey);
      expect(mockRepository.update).toHaveBeenCalledWith(1, { isActive: false });
      expect(mockRepository.create).toHaveBeenCalled();
    });

    it('should handle repository creation errors', async () => {
      mockRepository.findByUserId.mockResolvedValue(null);
      mockGenerator.generateApiKey.mockReturnValue('ak_test-key');
      mockGenerator.calculateExpirationDate.mockReturnValue(new Date());
      mockRepository.create.mockRejectedValue(new Error('Database error'));

      const result = await service.generateApiKey(validRequest);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to generate API key: Database error');
    });

    it('should handle generator errors', async () => {
      mockRepository.findByUserId.mockResolvedValue(null);
      mockGenerator.generateApiKey.mockImplementation(() => {
        throw new Error('Generation failed');
      });

      const result = await service.generateApiKey(validRequest);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to generate API key: Generation failed');
    });

    it('should handle request without metadata', async () => {
      const requestWithoutMetadata: IApiKeyCreateRequest = {
        userId: 'test-user-123'
      };
      const generatedKey = 'ak_generated-key-12345';
      const expirationDate = new Date(Date.now() + 60 * 60 * 1000);
      const createdApiKey = createMockApiKey({ 
        apiKey: generatedKey, 
        metadata: undefined 
      });

      mockRepository.findByUserId.mockResolvedValue(null);
      mockGenerator.generateApiKey.mockReturnValue(generatedKey);
      mockGenerator.calculateExpirationDate.mockReturnValue(expirationDate);
      mockRepository.create.mockResolvedValue(createdApiKey);

      const result = await service.generateApiKey(requestWithoutMetadata);

      expect(result.success).toBe(true);
      expect(mockRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          metadata: undefined
        })
      );
    });
  });

  describe('validateApiKey', () => {
    const validationRequest: IApiKeyValidationRequest = {
      encryptedApiKey: 'encrypted-key',
      userId: 'test-user-123'
    };

    it('should validate API key successfully', async () => {
      const mockApiKey = createMockApiKey();
      const validationResult = {
        isValid: true,
        isExpired: false,
        apiKey: mockApiKey
      };

      mockValidator.validateApiKey.mockResolvedValue(validationResult);

      const result = await service.validateApiKey(validationRequest);

      expect(result).toEqual(validationResult);
      expect(mockValidator.validateApiKey).toHaveBeenCalledWith(validationRequest);
    });

    it('should handle validation failure', async () => {
      const validationResult = {
        isValid: false,
        isExpired: false,
        error: 'Invalid API key'
      };

      mockValidator.validateApiKey.mockResolvedValue(validationResult);

      const result = await service.validateApiKey(validationRequest);

      expect(result).toEqual(validationResult);
    });

    it('should handle expired API key', async () => {
      const expiredApiKey = createExpiredApiKey();
      const validationResult = {
        isValid: false,
        isExpired: true,
        apiKey: expiredApiKey,
        error: 'API key has expired'
      };

      mockValidator.validateApiKey.mockResolvedValue(validationResult);

      const result = await service.validateApiKey(validationRequest);

      expect(result).toEqual(validationResult);
    });

    it('should handle validator errors', async () => {
      mockValidator.validateApiKey.mockRejectedValue(new Error('Validator error'));

      const result = await service.validateApiKey(validationRequest);

      expect(result.isValid).toBe(false);
      expect(result.isExpired).toBe(false);
      expect(result.error).toContain('Validation failed: Validator error');
    });
  });

  describe('revokeApiKey', () => {
    it('should revoke API key successfully', async () => {
      const existingKey = createMockApiKey({ id: 1 });
      
      mockRepository.findByUserId.mockResolvedValue(existingKey);
      mockRepository.update.mockResolvedValue(null);

      const result = await service.revokeApiKey('test-user-123');

      expect(result.success).toBe(true);
      expect(result.error).toBeUndefined();
      expect(mockRepository.update).toHaveBeenCalledWith(1, { isActive: false });
    });

    it('should handle case when no API key exists', async () => {
      mockRepository.findByUserId.mockResolvedValue(null);

      const result = await service.revokeApiKey('test-user-123');

      expect(result.success).toBe(false);
      expect(result.error).toBe('No active API key found for user');
      expect(mockRepository.update).not.toHaveBeenCalled();
    });

    it('should handle repository errors', async () => {
      const existingKey = createMockApiKey({ id: 1 });
      
      mockRepository.findByUserId.mockResolvedValue(existingKey);
      mockRepository.update.mockRejectedValue(new Error('Update failed'));

      const result = await service.revokeApiKey('test-user-123');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to revoke API key: Update failed');
    });

    it('should handle API key without ID', async () => {
      const keyWithoutId = createMockApiKey();
      delete keyWithoutId.id;
      
      mockRepository.findByUserId.mockResolvedValue(keyWithoutId);

      const result = await service.revokeApiKey('test-user-123');

      expect(result.success).toBe(false);
      expect(result.error).toBe('No active API key found for user');
    });
  });

  describe('cleanupExpiredKeys', () => {
    it('should cleanup expired keys successfully', async () => {
      mockRepository.deleteExpired.mockResolvedValue(5);

      const result = await service.cleanupExpiredKeys();

      expect(result.success).toBe(true);
      expect(result.deletedCount).toBe(5);
      expect(result.error).toBeUndefined();
    });

    it('should handle case when no expired keys exist', async () => {
      mockRepository.deleteExpired.mockResolvedValue(0);

      const result = await service.cleanupExpiredKeys();

      expect(result.success).toBe(true);
      expect(result.deletedCount).toBe(0);
    });

    it('should handle repository errors', async () => {
      mockRepository.deleteExpired.mockRejectedValue(new Error('Cleanup failed'));

      const result = await service.cleanupExpiredKeys();

      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to cleanup expired keys: Cleanup failed');
    });
  });

  describe('integration scenarios', () => {
    it('should handle multiple operations in sequence', async () => {
      const userId = 'test-user-123';
      const generatedKey = 'ak_test-key';
      const expirationDate = new Date(Date.now() + 60 * 60 * 1000);
      const createdApiKey = createMockApiKey({ 
        id: 1,
        userId,
        apiKey: generatedKey 
      });

      // Generate API key
      mockRepository.findByUserId.mockResolvedValueOnce(null);
      mockGenerator.generateApiKey.mockReturnValue(generatedKey);
      mockGenerator.calculateExpirationDate.mockReturnValue(expirationDate);
      mockRepository.create.mockResolvedValue(createdApiKey);

      const generateResult = await service.generateApiKey({ userId });
      expect(generateResult.success).toBe(true);

      // Validate API key
      mockValidator.validateApiKey.mockResolvedValue({
        isValid: true,
        isExpired: false,
        apiKey: createdApiKey
      });

      const validateResult = await service.validateApiKey({
        encryptedApiKey: 'encrypted-key',
        userId
      });
      expect(validateResult.isValid).toBe(true);

      // Revoke API key
      mockRepository.findByUserId.mockResolvedValueOnce(createdApiKey);
      mockRepository.update.mockResolvedValue(null);

      const revokeResult = await service.revokeApiKey(userId);
      expect(revokeResult.success).toBe(true);
    });
  });
});
