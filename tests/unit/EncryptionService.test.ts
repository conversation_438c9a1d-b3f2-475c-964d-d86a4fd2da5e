/*
  @method: tests generated using AI
  @validation: manually validated
*/

import { EncryptionService } from '../../src/shared/utils/EncryptionService';
import { EncryptionConfig } from '../../src/shared/types/ApiTypes';

describe('EncryptionService', () => {
  let encryptionService: EncryptionService;
  let config: EncryptionConfig;

  beforeEach(() => {
    config = {
      secret: 'test-secret-key-for-testing-only',
      algorithm: 'aes-256-cbc'
    };
    encryptionService = new EncryptionService(config);
  });

  describe('encrypt', () => {
    it('should encrypt text successfully', () => {
      const plaintext = 'test-api-key-12345';
      
      const encrypted = encryptionService.encrypt(plaintext);
      
      expect(encrypted).toBeDefined();
      expect(typeof encrypted).toBe('string');
      expect(encrypted).not.toBe(plaintext);
      expect(encrypted.split(':')).toHaveLength(2); // iv:encrypted
    });

    it('should produce different encrypted values for the same input', () => {
      const plaintext = 'test-api-key-12345';
      
      const encrypted1 = encryptionService.encrypt(plaintext);
      const encrypted2 = encryptionService.encrypt(plaintext);
      
      expect(encrypted1).not.toBe(encrypted2);
    });

    it('should handle empty string', () => {
      const plaintext = '';
      
      const encrypted = encryptionService.encrypt(plaintext);
      
      expect(encrypted).toBeDefined();
      expect(typeof encrypted).toBe('string');
    });

    it('should handle special characters', () => {
      const plaintext = 'test-key-with-special-chars!@#$%^&*()';
      
      const encrypted = encryptionService.encrypt(plaintext);
      
      expect(encrypted).toBeDefined();
      expect(typeof encrypted).toBe('string');
    });
  });

  describe('decrypt', () => {
    it('should decrypt encrypted text successfully', () => {
      const plaintext = 'test-api-key-12345';
      
      const encrypted = encryptionService.encrypt(plaintext);
      const decrypted = encryptionService.decrypt(encrypted);
      
      expect(decrypted).toBe(plaintext);
    });

    it('should handle empty string encryption/decryption', () => {
      const plaintext = '';
      
      const encrypted = encryptionService.encrypt(plaintext);
      const decrypted = encryptionService.decrypt(encrypted);
      
      expect(decrypted).toBe(plaintext);
    });

    it('should handle special characters encryption/decryption', () => {
      const plaintext = 'test-key-with-special-chars!@#$%^&*()';
      
      const encrypted = encryptionService.encrypt(plaintext);
      const decrypted = encryptionService.decrypt(encrypted);
      
      expect(decrypted).toBe(plaintext);
    });

    it('should throw error for invalid encrypted text format', () => {
      const invalidEncrypted = 'invalid-encrypted-text';
      
      expect(() => {
        encryptionService.decrypt(invalidEncrypted);
      }).toThrow('Invalid encrypted text format');
    });

    it('should throw error for malformed encrypted text', () => {
      const malformedEncrypted = 'invalid:format:too:many:parts';
      
      expect(() => {
        encryptionService.decrypt(malformedEncrypted);
      }).toThrow();
    });

    it('should throw error for corrupted encrypted data', () => {
      const corruptedEncrypted = 'abcd1234:corrupted-data';
      
      expect(() => {
        encryptionService.decrypt(corruptedEncrypted);
      }).toThrow('Decryption failed');
    });
  });

  describe('round-trip encryption/decryption', () => {
    const testCases = [
      'simple-text',
      'ak_1234567890abcdef',
      'test with spaces',
      'special!@#$%^&*()chars',
      '12345',
      'very-long-text-that-should-still-work-correctly-with-encryption-and-decryption-process',
      ''
    ];

    testCases.forEach(testCase => {
      it(`should handle round-trip for: "${testCase}"`, () => {
        const encrypted = encryptionService.encrypt(testCase);
        const decrypted = encryptionService.decrypt(encrypted);
        
        expect(decrypted).toBe(testCase);
      });
    });
  });

  describe('different instances with same config', () => {
    it('should be able to decrypt data encrypted by another instance', () => {
      const plaintext = 'test-cross-instance-compatibility';
      const service1 = new EncryptionService(config);
      const service2 = new EncryptionService(config);
      
      const encrypted = service1.encrypt(plaintext);
      const decrypted = service2.decrypt(encrypted);
      
      expect(decrypted).toBe(plaintext);
    });
  });

  describe('error handling', () => {
    it('should handle encryption errors gracefully', () => {
      // Mock crypto.scryptSync to throw an error
      const originalScryptSync = require('crypto').scryptSync;
      require('crypto').scryptSync = jest.fn().mockImplementation(() => {
        throw new Error('Crypto error');
      });

      expect(() => {
        new EncryptionService(config);
      }).toThrow();

      // Restore original function
      require('crypto').scryptSync = originalScryptSync;
    });
  });
});
