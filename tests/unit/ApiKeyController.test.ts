/*
  @method: tests generated using AI
  @validation: manually validated
*/

import { Request, Response } from 'express';
import { ApiKeyController } from '../../src/apiServer/controllers/ApiKeyController';
import { IApiKeyService } from '../../src/shared/interfaces/IServices';
import { createMockApiKey, createExpiredApiKey } from '../utils/testHelpers';

describe('ApiKeyController', () => {
  let controller: ApiKeyController;
  let mockService: jest.Mocked<IApiKeyService>;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;

  beforeEach(() => {
    mockService = {
      generateApiKey: jest.fn(),
      validateApiKey: jest.fn(),
      revokeApiKey: jest.fn(),
    };

    mockRequest = {
      body: {}
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };

    controller = new ApiKeyController(mockService);
  });

  describe('generateApiKey', () => {
    it('should generate API key successfully', async () => {
      const requestBody = {
        userId: 'test-user-123',
        metadata: { purpose: 'testing' }
      };
      mockRequest.body = requestBody;

      const serviceResult = {
        success: true,
        apiKey: 'ak_generated-key-12345',
        expiresAt: new Date('2024-01-01T12:00:00Z')
      };
      mockService.generateApiKey.mockResolvedValue(serviceResult);

      await controller.generateApiKey(mockRequest as Request, mockResponse as Response);

      expect(mockService.generateApiKey).toHaveBeenCalledWith(requestBody);
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: {
          apiKey: serviceResult.apiKey,
          expiresAt: serviceResult.expiresAt
        },
        message: 'API key generated successfully',
        timestamp: expect.any(String)
      });
    });

    it('should return 400 when userId is missing', async () => {
      mockRequest.body = { metadata: { purpose: 'testing' } };

      await controller.generateApiKey(mockRequest as Request, mockResponse as Response);

      expect(mockService.generateApiKey).not.toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'userId is required',
        timestamp: expect.any(String)
      });
    });

    it('should handle service failure', async () => {
      mockRequest.body = { userId: 'test-user-123' };

      const serviceResult = {
        success: false,
        error: 'User already has an active API key'
      };
      mockService.generateApiKey.mockResolvedValue(serviceResult);

      await controller.generateApiKey(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: serviceResult.error,
        timestamp: expect.any(String)
      });
    });

    it('should handle service exceptions', async () => {
      mockRequest.body = { userId: 'test-user-123' };
      mockService.generateApiKey.mockRejectedValue(new Error('Service error'));

      await controller.generateApiKey(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Internal server error: Service error',
        timestamp: expect.any(String)
      });
    });

    it('should handle request without metadata', async () => {
      mockRequest.body = { userId: 'test-user-123' };

      const serviceResult = {
        success: true,
        apiKey: 'ak_generated-key-12345',
        expiresAt: new Date()
      };
      mockService.generateApiKey.mockResolvedValue(serviceResult);

      await controller.generateApiKey(mockRequest as Request, mockResponse as Response);

      expect(mockService.generateApiKey).toHaveBeenCalledWith({
        userId: 'test-user-123',
        metadata: undefined
      });
      expect(mockResponse.status).toHaveBeenCalledWith(201);
    });
  });

  describe('validateApiKey', () => {
    it('should validate API key successfully', async () => {
      const requestBody = {
        encryptedApiKey: 'encrypted-key',
        userId: 'test-user-123'
      };
      mockRequest.body = requestBody;

      const mockApiKey = createMockApiKey();
      const serviceResult = {
        isValid: true,
        isExpired: false,
        apiKey: mockApiKey
      };
      mockService.validateApiKey.mockResolvedValue(serviceResult);

      await controller.validateApiKey(mockRequest as Request, mockResponse as Response);

      expect(mockService.validateApiKey).toHaveBeenCalledWith(requestBody);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: {
          isValid: true,
          apiKey: {
            userId: mockApiKey.userId,
            createdAt: mockApiKey.createdAt,
            expiresAt: mockApiKey.expiresAt,
            metadata: mockApiKey.metadata
          }
        },
        message: 'API key is valid',
        timestamp: expect.any(String)
      });
    });

    it('should return 400 when required fields are missing', async () => {
      mockRequest.body = { userId: 'test-user-123' }; // Missing encryptedApiKey

      await controller.validateApiKey(mockRequest as Request, mockResponse as Response);

      expect(mockService.validateApiKey).not.toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'encryptedApiKey and userId are required',
        timestamp: expect.any(String)
      });
    });

    it('should handle invalid API key with 403 status', async () => {
      mockRequest.body = {
        encryptedApiKey: 'encrypted-key',
        userId: 'test-user-123'
      };

      const serviceResult = {
        isValid: false,
        isExpired: false,
        error: 'API key mismatch'
      };
      mockService.validateApiKey.mockResolvedValue(serviceResult);

      await controller.validateApiKey(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        data: {
          isValid: false,
          isExpired: false
        },
        error: serviceResult.error,
        timestamp: expect.any(String)
      });
    });

    it('should handle expired API key with 401 status', async () => {
      mockRequest.body = {
        encryptedApiKey: 'encrypted-key',
        userId: 'test-user-123'
      };

      const expiredApiKey = createExpiredApiKey();
      const serviceResult = {
        isValid: false,
        isExpired: true,
        apiKey: expiredApiKey,
        error: 'API key has expired'
      };
      mockService.validateApiKey.mockResolvedValue(serviceResult);

      await controller.validateApiKey(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        data: {
          isValid: false,
          isExpired: true
        },
        error: serviceResult.error,
        timestamp: expect.any(String)
      });
    });

    it('should handle service exceptions', async () => {
      mockRequest.body = {
        encryptedApiKey: 'encrypted-key',
        userId: 'test-user-123'
      };
      mockService.validateApiKey.mockRejectedValue(new Error('Service error'));

      await controller.validateApiKey(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Internal server error: Service error',
        timestamp: expect.any(String)
      });
    });
  });

  describe('revokeApiKey', () => {
    it('should revoke API key successfully', async () => {
      mockRequest.body = { userId: 'test-user-123' };

      const serviceResult = { success: true };
      mockService.revokeApiKey.mockResolvedValue(serviceResult);

      await controller.revokeApiKey(mockRequest as Request, mockResponse as Response);

      expect(mockService.revokeApiKey).toHaveBeenCalledWith('test-user-123');
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'API key revoked successfully',
        timestamp: expect.any(String)
      });
    });

    it('should return 400 when userId is missing', async () => {
      mockRequest.body = {};

      await controller.revokeApiKey(mockRequest as Request, mockResponse as Response);

      expect(mockService.revokeApiKey).not.toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'userId is required',
        timestamp: expect.any(String)
      });
    });

    it('should handle service failure with 404 status', async () => {
      mockRequest.body = { userId: 'test-user-123' };

      const serviceResult = {
        success: false,
        error: 'No active API key found for user'
      };
      mockService.revokeApiKey.mockResolvedValue(serviceResult);

      await controller.revokeApiKey(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: serviceResult.error,
        timestamp: expect.any(String)
      });
    });

    it('should handle service exceptions', async () => {
      mockRequest.body = { userId: 'test-user-123' };
      mockService.revokeApiKey.mockRejectedValue(new Error('Service error'));

      await controller.revokeApiKey(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Internal server error: Service error',
        timestamp: expect.any(String)
      });
    });
  });

  describe('error handling edge cases', () => {
    it('should handle unknown errors gracefully', async () => {
      mockRequest.body = { userId: 'test-user-123' };
      mockService.generateApiKey.mockRejectedValue('String error');

      await controller.generateApiKey(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Internal server error: Unknown error',
        timestamp: expect.any(String)
      });
    });

    it('should handle null/undefined errors', async () => {
      mockRequest.body = { userId: 'test-user-123' };
      mockService.generateApiKey.mockRejectedValue(null);

      await controller.generateApiKey(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Internal server error: Unknown error',
        timestamp: expect.any(String)
      });
    });
  });

  describe('response format consistency', () => {
    it('should always include timestamp in responses', async () => {
      mockRequest.body = { userId: 'test-user-123' };
      mockService.generateApiKey.mockResolvedValue({
        success: true,
        apiKey: 'ak_test',
        expiresAt: new Date()
      });

      await controller.generateApiKey(mockRequest as Request, mockResponse as Response);

      const responseCall = (mockResponse.json as jest.Mock).mock.calls[0][0];
      expect(responseCall.timestamp).toBeDefined();
      expect(typeof responseCall.timestamp).toBe('string');
      expect(new Date(responseCall.timestamp)).toBeInstanceOf(Date);
    });

    it('should maintain consistent response structure across all endpoints', async () => {
      const endpoints = [
        {
          method: controller.generateApiKey,
          body: { userId: 'test' },
          mockSetup: () => mockService.generateApiKey.mockResolvedValue({ success: true, apiKey: 'ak_test', expiresAt: new Date() })
        },
        {
          method: controller.validateApiKey,
          body: { encryptedApiKey: 'encrypted', userId: 'test' },
          mockSetup: () => mockService.validateApiKey.mockResolvedValue({ isValid: true, isExpired: false })
        },
        {
          method: controller.revokeApiKey,
          body: { userId: 'test' },
          mockSetup: () => mockService.revokeApiKey.mockResolvedValue({ success: true })
        }
      ];

      for (const endpoint of endpoints) {
        mockRequest.body = endpoint.body;
        endpoint.mockSetup();

        await endpoint.method(mockRequest as Request, mockResponse as Response);

        const responseCall = (mockResponse.json as jest.Mock).mock.calls.slice(-1)[0][0];
        expect(responseCall).toHaveProperty('success');
        expect(responseCall).toHaveProperty('timestamp');
        expect(typeof responseCall.success).toBe('boolean');
        expect(typeof responseCall.timestamp).toBe('string');
      }
    });
  });
});
