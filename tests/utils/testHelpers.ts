/*
  @method: tests generated using AI
  @validation: manually validated
*/

import { IApi<PERSON>ey } from '../../src/shared/interfaces/IApiKey';
import { ApiKeyConfig, DatabaseConfig, EncryptionConfig } from '../../src/shared/types/ApiTypes';
import * as fs from 'fs';

export const createMockApiKey = (overrides: Partial<IApiKey> = {}): IApiKey => {
  const now = new Date();
  const expiresAt = new Date(now.getTime() + 60 * 60 * 1000); // 1 hour from now
  
  return {
    id: 1,
    userId: 'test-user-123',
    apiKey: 'ak_test-api-key-12345',
    createdAt: now,
    expiresAt,
    isActive: true,
    metadata: { purpose: 'testing' },
    ...overrides
  };
};

export const createTestConfigs = () => {
  const apiKeyConfig: ApiKeyConfig = {
    ttlHours: 1,
    keyLength: 16
  };

  const databaseConfig: DatabaseConfig = {
    path: ':memory:' // Use in-memory database for reliable testing
  };

  const encryptionConfig: EncryptionConfig = {
    secret: 'test-secret-key-for-testing-only',
    algorithm: 'aes-256-cbc'
  };

  return { apiKeyConfig, databaseConfig, encryptionConfig };
};

export const createExpiredApiKey = (overrides: Partial<IApiKey> = {}): IApiKey => {
  const now = new Date();
  const pastDate = new Date(now.getTime() - 60 * 60 * 1000); // 1 hour ago
  
  return createMockApiKey({
    expiresAt: pastDate,
    ...overrides
  });
};

export const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

export const generateRandomUserId = (): string => {
  return `test-user-${Math.random().toString(36).substr(2, 9)}`;
};



export async function cleanupTestDatabase(dbPath: string): Promise<void> {
  // No cleanup needed for in-memory databases
  if (dbPath === ':memory:') {
    return;
  }

  try {
    if (fs.existsSync(dbPath)) {
      fs.unlinkSync(dbPath);
    }
  } catch (error) {
    // Ignore cleanup errors
  }
}
