/*
  @method: tests generated using AI
  @validation: manually validated
*/

import * as fs from 'fs';
import * as path from 'path';

// Global test setup
beforeAll(() => {
  // Ensure test directories exist
  const testDataDir = path.join(__dirname, '../data/test');
  const testLogsDir = path.join(__dirname, '../logs/test');

  if (!fs.existsSync(testDataDir)) {
    fs.mkdirSync(testDataDir, { recursive: true });
  }

  if (!fs.existsSync(testLogsDir)) {
    fs.mkdirSync(testLogsDir, { recursive: true });
  }

  // Set proper permissions for test directories
  try {
    fs.chmodSync(testDataDir, 0o755);
    fs.chmodSync(testLogsDir, 0o755);
  } catch (error) {
    // Ignore permission errors in test environment
  }
});

// Global test cleanup
afterAll(() => {
  // Clean up test databases
  const testDataDir = path.join(__dirname, '../data/test');
  if (fs.existsSync(testDataDir)) {
    const files = fs.readdirSync(testDataDir);
    files.forEach(file => {
      if (file.endsWith('.sqlite')) {
        fs.unlinkSync(path.join(testDataDir, file));
      }
    });
  }
});

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.DB_PATH = `/tmp/test-database-${Date.now()}.sqlite`;
process.env.LOG_LEVEL = 'error';
process.env.ENCRYPTION_SECRET = 'test-secret-key-for-testing-only';
process.env.ENCRYPTION_ALGORITHM = 'aes-256-cbc';
process.env.API_KEY_TTL_HOURS = '1';
process.env.API_KEY_LENGTH = '16';
