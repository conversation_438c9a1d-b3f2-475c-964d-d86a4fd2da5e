/*
  @method: tests generated using AI
  @validation: manually validated
*/

import request from 'supertest';
import express from 'express';
import { ApiServer } from '../../src/apiServer';
import { EncryptionService } from '../../src/shared/utils/EncryptionService';
import { createTestConfigs, generateRandomUserId } from '../utils/testHelpers';

describe('API Endpoints Integration Tests', () => {
  let app: express.Application;
  let apiServer: ApiServer;
  let encryptionService: EncryptionService;
  // use in-memory database for reliable testing
  const testDbPath = ':memory:';

  beforeAll(async () => {
    // no cleanup needed for in-memory database

    const { apiKeyConfig, databaseConfig, encryptionConfig } = createTestConfigs();
    databaseConfig.path = testDbPath;

    // create Express app
    app = express();
    app.use(express.json());

    // initialize API server
    apiServer = new ApiServer(apiKeyConfig, databaseConfig, encryptionConfig);
    app.use('/api', apiServer.getRouter());

    // initialize encryption service for testing
    encryptionService = new EncryptionService(encryptionConfig);

    // wait for database initialization to complete
    await new Promise(resolve => setTimeout(resolve, 100));
  });

  afterAll(async () => {
    if (apiServer) {
      await apiServer.cleanup();
    }
    // no cleanup needed for in-memory database
  });

  describe('POST /api/keys/generate', () => {
    it('should generate API key successfully', async () => {
      const userId = generateRandomUserId();
      const requestBody = {
        userId,
        metadata: { purpose: 'integration-testing' }
      };

      const response = await request(app)
        .post('/api/keys/generate')
        .send(requestBody)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.apiKey).toBeDefined();
      expect(response.body.data.apiKey).toMatch(/^ak_/);
      expect(response.body.data.expiresAt).toBeDefined();
      expect(response.body.message).toBe('API key generated successfully');
      expect(response.body.timestamp).toBeDefined();
    });

    it('should reject duplicate API key generation for same user', async () => {
      const userId = generateRandomUserId();
      const requestBody = { userId };

      // generate first API key
      await request(app)
        .post('/api/keys/generate')
        .send(requestBody)
        .expect(201);

      // try to generate second API key for same user
      const response = await request(app)
        .post('/api/keys/generate')
        .send(requestBody)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('already has an active API key');
    });

    it('should return 400 for missing userId', async () => {
      const response = await request(app)
        .post('/api/keys/generate')
        .send({ metadata: { purpose: 'test' } })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('userId is required');
    });

    it('should handle empty request body', async () => {
      const response = await request(app)
        .post('/api/keys/generate')
        .send({})
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('userId is required');
    });

    it('should generate API key without metadata', async () => {
      const userId = generateRandomUserId();

      const response = await request(app)
        .post('/api/keys/generate')
        .send({ userId })
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.apiKey).toBeDefined();
    });
  });

  describe('POST /api/keys/validate', () => {
    let testUserId: string;
    let testApiKey: string;

    beforeEach(async () => {
      testUserId = generateRandomUserId();
      
      // generate API key for testing
      const generateResponse = await request(app)
        .post('/api/keys/generate')
        .send({ userId: testUserId })
        .expect(201);
      
      testApiKey = generateResponse.body.data.apiKey;
    });

    it('should validate API key successfully', async () => {
      const encryptedApiKey = encryptionService.encrypt(testApiKey);
      
      const response = await request(app)
        .post('/api/keys/validate')
        .send({
          encryptedApiKey,
          userId: testUserId
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.isValid).toBe(true);
      expect(response.body.data.apiKey.userId).toBe(testUserId);
      expect(response.body.message).toBe('API key is valid');
    });

    it('should reject invalid encrypted API key', async () => {
      const response = await request(app)
        .post('/api/keys/validate')
        .send({
          encryptedApiKey: 'invalid-encrypted-key',
          userId: testUserId
        })
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.data.isValid).toBe(false);
      expect(response.body.error).toContain('Invalid encrypted API key format');
    });

    it('should reject API key for wrong user', async () => {
      const encryptedApiKey = encryptionService.encrypt(testApiKey);
      const wrongUserId = generateRandomUserId();
      
      const response = await request(app)
        .post('/api/keys/validate')
        .send({
          encryptedApiKey,
          userId: wrongUserId
        })
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.data.isValid).toBe(false);
      expect(response.body.error).toBe('No API key found for user');
    });

    it('should reject mismatched API key', async () => {
      const wrongApiKey = 'ak_wrong-api-key-12345';
      const encryptedWrongKey = encryptionService.encrypt(wrongApiKey);
      
      const response = await request(app)
        .post('/api/keys/validate')
        .send({
          encryptedApiKey: encryptedWrongKey,
          userId: testUserId
        })
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.data.isValid).toBe(false);
      expect(response.body.error).toBe('API key mismatch');
    });

    it('should return 400 for missing required fields', async () => {
      const response = await request(app)
        .post('/api/keys/validate')
        .send({ userId: testUserId })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('encryptedApiKey and userId are required');
    });
  });

  describe('DELETE /api/keys/revoke', () => {
    let testUserId: string;

    beforeEach(async () => {
      testUserId = generateRandomUserId();
      
      // generate API key for testing
      await request(app)
        .post('/api/keys/generate')
        .send({ userId: testUserId })
        .expect(201);
    });

    it('should revoke API key successfully', async () => {
      const response = await request(app)
        .delete('/api/keys/revoke')
        .send({ userId: testUserId })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('API key revoked successfully');
    });

    it('should return 404 for non-existent user', async () => {
      const nonExistentUserId = generateRandomUserId();
      
      const response = await request(app)
        .delete('/api/keys/revoke')
        .send({ userId: nonExistentUserId })
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('No active API key found for user');
    });

    it('should return 400 for missing userId', async () => {
      const response = await request(app)
        .delete('/api/keys/revoke')
        .send({})
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('userId is required');
    });

    it('should not allow validation after revocation', async () => {
      // girst, get the API key
      const generateResponse = await request(app)
        .post('/api/keys/generate')
        .send({ userId: generateRandomUserId() })
        .expect(201);
      
      const apiKey = generateResponse.body.data.apiKey;
      const userId = generateRandomUserId();
      
      // generate key for the test user
      await request(app)
        .post('/api/keys/generate')
        .send({ userId })
        .expect(201);
      
      // revoke the key
      await request(app)
        .delete('/api/keys/revoke')
        .send({ userId })
        .expect(200);

      // try to validate the revoked key
      const encryptedApiKey = encryptionService.encrypt(apiKey);
      const response = await request(app)
        .post('/api/keys/validate')
        .send({ encryptedApiKey, userId })
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.data.isValid).toBe(false);
    });
  });

  describe('End-to-end workflow', () => {
    it('should handle complete API key lifecycle', async () => {
      const userId = generateRandomUserId();
      const metadata = { purpose: 'e2e-testing', permissions: ['read', 'write'] };

      // 1. generate API key
      const generateResponse = await request(app)
        .post('/api/keys/generate')
        .send({ userId, metadata })
        .expect(201);

      const apiKey = generateResponse.body.data.apiKey;
      expect(apiKey).toMatch(/^ak_/);

      // 2. validate API key
      const encryptedApiKey = encryptionService.encrypt(apiKey);
      const validateResponse = await request(app)
        .post('/api/keys/validate')
        .send({ encryptedApiKey, userId })
        .expect(200);

      expect(validateResponse.body.data.isValid).toBe(true);
      expect(validateResponse.body.data.apiKey.metadata).toEqual(metadata);

      // 3. revoke API key
      await request(app)
        .delete('/api/keys/revoke')
        .send({ userId })
        .expect(200);

      // 4. verify key is no longer valid
      await request(app)
        .post('/api/keys/validate')
        .send({ encryptedApiKey, userId })
        .expect(403);

      // 5. generate new API key after revocation
      const newGenerateResponse = await request(app)
        .post('/api/keys/generate')
        .send({ userId })
        .expect(201);

      expect(newGenerateResponse.body.data.apiKey).not.toBe(apiKey);
    });

    it('should handle multiple users independently', async () => {
      const user1 = generateRandomUserId();
      const user2 = generateRandomUserId();

      // generate keys for both users
      const response1 = await request(app)
        .post('/api/keys/generate')
        .send({ userId: user1 })
        .expect(201);

      const response2 = await request(app)
        .post('/api/keys/generate')
        .send({ userId: user2 })
        .expect(201);

      const apiKey1 = response1.body.data.apiKey;
      const apiKey2 = response2.body.data.apiKey;

      expect(apiKey1).not.toBe(apiKey2);

      // validate both keys
      const encrypted1 = encryptionService.encrypt(apiKey1);
      const encrypted2 = encryptionService.encrypt(apiKey2);

      await request(app)
        .post('/api/keys/validate')
        .send({ encryptedApiKey: encrypted1, userId: user1 })
        .expect(200);

      await request(app)
        .post('/api/keys/validate')
        .send({ encryptedApiKey: encrypted2, userId: user2 })
        .expect(200);

      // cross-validation should fail
      await request(app)
        .post('/api/keys/validate')
        .send({ encryptedApiKey: encrypted1, userId: user2 })
        .expect(403);

      await request(app)
        .post('/api/keys/validate')
        .send({ encryptedApiKey: encrypted2, userId: user1 })
        .expect(403);
    });
  });

  describe('Error handling', () => {
    it('should handle malformed JSON', async () => {
      await request(app)
        .post('/api/keys/generate')
        .set('Content-Type', 'application/json')
        .send('{"invalid": json}')
        .expect(400);
    });

    it('should handle large payloads gracefully', async () => {
      const largeMetadata = {
        data: 'x'.repeat(10000) // 10KB of data
      };

      const response = await request(app)
        .post('/api/keys/generate')
        .send({
          userId: generateRandomUserId(),
          metadata: largeMetadata
        })
        .expect(201);

      expect(response.body.success).toBe(true);
    });
  });
});
