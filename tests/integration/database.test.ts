/*
  @method: tests generated using AI
  @validation: manually validated
*/

import { ApiKeyRepository } from '../../src/apiServer/repositories/ApiKeyRepository';
import { DatabaseConfig } from '../../src/shared/types/ApiTypes';
import { createMockApiKey } from '../utils/testHelpers';

describe('Database Integration Tests', () => {
  let repository: ApiKeyRepository;
  const testDbPath = ':memory:'; // Use in-memory database for reliable testing

  beforeEach(async () => {
    // No cleanup needed for in-memory database
    const config: DatabaseConfig = { path: testDbPath };
    repository = new ApiKeyRepository(config);

    // Wait for database initialization to complete
    await new Promise(resolve => setTimeout(resolve, 100));
  });

  afterEach(async () => {
    if (repository) {
      try {
        repository.close();
      } catch (error) {
        // Ignore close errors in tests
      }
    }
    // No cleanup needed for in-memory database
  });

  describe('Database initialization', () => {
    it('should create database and tables', async () => {
      // For in-memory database, we test by creating a record
      const apiKeyData = createMockApiKey();
      delete apiKeyData.id;

      const created = await repository.create(apiKeyData);
      expect(created.id).toBeDefined();
      expect(created.userId).toBe(apiKeyData.userId);
    });

    it('should create proper table structure', async () => {
      // Test by inserting and retrieving data
      const apiKeyData = createMockApiKey();
      delete apiKeyData.id;

      const created = await repository.create(apiKeyData);
      expect(created.id).toBeDefined();
      expect(created.userId).toBe(apiKeyData.userId);

      // Verify we can query the data
      const found = await repository.findByUserId(apiKeyData.userId);
      expect(found).toBeDefined();
      expect(found!.id).toBe(created.id);
    });
  });

  describe('CRUD operations', () => {
    it('should perform complete CRUD cycle', async () => {
      const apiKeyData = createMockApiKey();
      delete apiKeyData.id;

      // Create
      const created = await repository.create(apiKeyData);
      expect(created.id).toBeDefined();
      expect(created.userId).toBe(apiKeyData.userId);

      // Read
      const found = await repository.findByUserId(apiKeyData.userId);
      expect(found).toBeDefined();
      expect(found!.id).toBe(created.id);

      // Update
      const updated = await repository.update(created.id!, { isActive: false });
      expect(updated).toBeDefined();
      expect(updated!.isActive).toBe(false);

      // Delete
      const deleted = await repository.delete(created.id!);
      expect(deleted).toBe(true);

      // Verify deletion
      const notFound = await repository.findByUserId(apiKeyData.userId);
      expect(notFound).toBeNull();
    });

    it('should handle concurrent operations', async () => {
      const users = ['user1', 'user2', 'user3', 'user4', 'user5'];
      const promises = users.map(userId => {
        const apiKeyData = createMockApiKey({ userId, apiKey: `ak_${userId}` });
        delete apiKeyData.id;
        return repository.create(apiKeyData);
      });

      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(5);
      results.forEach((result, index) => {
        expect(result.userId).toBe(users[index]);
        expect(result.id).toBeDefined();
      });

      // Verify all were created
      for (const userId of users) {
        const found = await repository.findByUserId(userId);
        expect(found).toBeDefined();
        expect(found!.userId).toBe(userId);
      }
    });
  });

  describe('Data integrity', () => {
    it('should maintain referential integrity', async () => {
      const apiKeyData = createMockApiKey();
      delete apiKeyData.id;

      const created = await repository.create(apiKeyData);
      
      // Try to create duplicate API key (should fail)
      await expect(repository.create(apiKeyData)).rejects.toThrow();
      
      // Original record should still exist
      const found = await repository.findByUserId(apiKeyData.userId);
      expect(found).toBeDefined();
      expect(found!.id).toBe(created.id);
    });

    it('should handle special characters in data', async () => {
      const specialData = createMockApiKey({
        userId: 'user-with-special-chars!@#$%^&*()',
        apiKey: 'ak_special-chars-key-!@#$%^&*()',
        metadata: {
          description: 'Special chars: !@#$%^&*()_+-=[]{}|;:,.<>?',
          unicode: '🔑🚀✨🎉',
          quotes: 'Single \'quotes\' and "double quotes"',
          backslashes: 'Path\\to\\file'
        }
      });
      delete specialData.id;

      await repository.create(specialData);
      const found = await repository.findByUserId(specialData.userId);

      expect(found).toBeDefined();
      expect(found!.userId).toBe(specialData.userId);
      expect(found!.apiKey).toBe(specialData.apiKey);
      expect(found!.metadata).toEqual(specialData.metadata);
    });

    it('should handle null and undefined metadata', async () => {
      const withUndefined = createMockApiKey({ metadata: undefined });
      delete withUndefined.id;

      await repository.create(withUndefined);
      const found = await repository.findByUserId(withUndefined.userId);

      expect(found).toBeDefined();
      expect(found!.metadata).toBeUndefined();
    });
  });

  describe('Date handling', () => {
    it('should store and retrieve dates accurately', async () => {
      const now = new Date();
      const future = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24 hours later
      
      const apiKeyData = createMockApiKey({
        createdAt: now,
        expiresAt: future
      });
      delete apiKeyData.id;

      await repository.create(apiKeyData);
      const found = await repository.findByUserId(apiKeyData.userId);

      expect(found).toBeDefined();
      // Allow for small differences due to database precision
      expect(Math.abs(found!.createdAt.getTime() - now.getTime())).toBeLessThan(1000);
      expect(Math.abs(found!.expiresAt.getTime() - future.getTime())).toBeLessThan(1000);
    });

    it('should handle timezone conversions correctly', async () => {
      const utcDate = new Date('2024-01-01T12:00:00.000Z');
      const apiKeyData = createMockApiKey({
        createdAt: utcDate,
        expiresAt: utcDate
      });
      delete apiKeyData.id;

      await repository.create(apiKeyData);
      const found = await repository.findByUserId(apiKeyData.userId);

      expect(found).toBeDefined();
      expect(found!.createdAt.toISOString()).toBe(utcDate.toISOString());
      expect(found!.expiresAt.toISOString()).toBe(utcDate.toISOString());
    });
  });

  describe('Query performance and indexing', () => {
    it('should efficiently query by user ID with large dataset', async () => {
      // Create many records
      const numRecords = 1000;
      const promises = [];
      
      for (let i = 0; i < numRecords; i++) {
        const apiKeyData = createMockApiKey({
          userId: `user-${i}`,
          apiKey: `ak_key-${i}`
        });
        delete apiKeyData.id;
        promises.push(repository.create(apiKeyData));
      }

      await Promise.all(promises);

      // Query should be fast due to indexing
      const startTime = Date.now();
      const found = await repository.findByUserId('user-500');
      const queryTime = Date.now() - startTime;

      expect(found).toBeDefined();
      expect(found!.userId).toBe('user-500');
      expect(queryTime).toBeLessThan(100); // Should be very fast with proper indexing
    });

    it('should efficiently query by API key with large dataset', async () => {
      // Create many records
      const numRecords = 500;
      const promises = [];
      
      for (let i = 0; i < numRecords; i++) {
        const apiKeyData = createMockApiKey({
          userId: `user-${i}`,
          apiKey: `ak_key-${i}`
        });
        delete apiKeyData.id;
        promises.push(repository.create(apiKeyData));
      }

      await Promise.all(promises);

      // Query should be fast due to indexing
      const startTime = Date.now();
      const found = await repository.findByApiKey('ak_key-250');
      const queryTime = Date.now() - startTime;

      expect(found).toBeDefined();
      expect(found!.apiKey).toBe('ak_key-250');
      expect(queryTime).toBeLessThan(100); // Should be very fast with proper indexing
    });
  });

  describe('Expired key cleanup', () => {
    it('should delete only expired keys', async () => {
      const activeKey = createMockApiKey({ userId: 'active-user' });
      // Use very old dates to ensure they're definitely expired
      const veryOldDate = new Date('2020-01-01T00:00:00.000Z');
      const expiredKey1 = createMockApiKey({
        userId: 'expired-user-1',
        apiKey: 'ak_expired-1',
        expiresAt: veryOldDate
      });
      const expiredKey2 = createMockApiKey({
        userId: 'expired-user-2',
        apiKey: 'ak_expired-2',
        expiresAt: veryOldDate
      });

      delete activeKey.id;
      delete expiredKey1.id;
      delete expiredKey2.id;

      await repository.create(activeKey);
      await repository.create(expiredKey1);
      await repository.create(expiredKey2);

      const deletedCount = await repository.deleteExpired();
      expect(deletedCount).toBeGreaterThanOrEqual(2);

      // Active key should still exist
      const foundActive = await repository.findByUserId('active-user');
      expect(foundActive).toBeDefined();

      // Expired keys should be gone
      const foundExpired1 = await repository.findByUserId('expired-user-1');
      const foundExpired2 = await repository.findByUserId('expired-user-2');
      expect(foundExpired1).toBeNull();
      expect(foundExpired2).toBeNull();
    });

    it('should handle cleanup with no expired keys', async () => {
      const activeKey = createMockApiKey();
      delete activeKey.id;
      
      await repository.create(activeKey);
      
      const deletedCount = await repository.deleteExpired();
      expect(deletedCount).toBe(0);

      // Active key should still exist
      const found = await repository.findByUserId(activeKey.userId);
      expect(found).toBeDefined();
    });
  });

  describe('Transaction handling', () => {
    it('should handle database errors gracefully', async () => {
      // Close the database to simulate connection issues
      repository.close();
      
      const apiKeyData = createMockApiKey();
      delete apiKeyData.id;

      await expect(repository.create(apiKeyData)).rejects.toThrow();
    });

    it('should maintain consistency during failures', async () => {
      const apiKeyData = createMockApiKey();
      delete apiKeyData.id;

      // Create a record
      const created = await repository.create(apiKeyData);
      
      // Verify it exists
      let found = await repository.findByUserId(apiKeyData.userId);
      expect(found).toBeDefined();

      // Try to create duplicate (should fail)
      await expect(repository.create(apiKeyData)).rejects.toThrow();
      
      // Original record should still exist and be unchanged
      found = await repository.findByUserId(apiKeyData.userId);
      expect(found).toBeDefined();
      expect(found!.id).toBe(created.id);
      expect(found!.isActive).toBe(created.isActive);
    });
  });

  describe('Multiple database instances', () => {
    it('should handle multiple repository instances on same database', async () => {
      // For in-memory databases, each instance is separate
      // Test that each instance works independently
      const config: DatabaseConfig = { path: ':memory:' };
      const repository2 = new ApiKeyRepository(config);

      // Wait for initialization
      await new Promise(resolve => setTimeout(resolve, 100));

      try {
        const apiKeyData1 = createMockApiKey({ userId: 'user1', apiKey: 'ak_user1' });
        const apiKeyData2 = createMockApiKey({ userId: 'user2', apiKey: 'ak_user2' });
        delete apiKeyData1.id;
        delete apiKeyData2.id;

        // Create with first instance
        const created1 = await repository.create(apiKeyData1);

        // Create with second instance
        const created2 = await repository2.create(apiKeyData2);

        // Verify each instance has its own data
        const found1 = await repository.findByUserId(apiKeyData1.userId);
        const found2 = await repository2.findByUserId(apiKeyData2.userId);

        expect(found1).toBeDefined();
        expect(found1!.id).toBe(created1.id);
        expect(found2).toBeDefined();
        expect(found2!.id).toBe(created2.id);
      } finally {
        repository2.close();
      }
    });
  });

  describe('Data migration and schema changes', () => {
    it('should handle database initialization correctly', async () => {
      // For in-memory databases, test that initialization works properly
      const config: DatabaseConfig = { path: ':memory:' };
      const newRepository = new ApiKeyRepository(config);

      // Wait for initialization
      await new Promise(resolve => setTimeout(resolve, 100));

      try {
        // Test that we can create and retrieve data
        const apiKeyData = createMockApiKey();
        delete apiKeyData.id;

        const created = await newRepository.create(apiKeyData);
        const found = await newRepository.findByUserId(apiKeyData.userId);

        expect(found).toBeDefined();
        expect(found!.id).toBe(created.id);
        expect(found!.userId).toBe(apiKeyData.userId);
      } finally {
        newRepository.close();
      }
    });
  });
});
